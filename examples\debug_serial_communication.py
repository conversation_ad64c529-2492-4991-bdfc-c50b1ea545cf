#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
串口通信深度诊断工具

专门用于诊断IM948串口通信问题，包括：
1. 原始数据监控
2. 详细时序分析
3. 应答匹配调试
4. 缓冲区状态监控

Author: Augment Agent
Date: 2025-08-02
"""

import sys
import os
import time
import threading
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    import serial
    import serial.tools.list_ports
    SERIAL_AVAILABLE = True
except ImportError:
    SERIAL_AVAILABLE = False
    print("警告: pyserial库未安装")

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s.%(msecs)03d - %(name)s - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('debug_serial_communication.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)


class SerialDiagnosticTool:
    """串口诊断工具"""
    
    def __init__(self, port='COM6', baudrate=115200):
        """初始化诊断工具"""
        self.port = port
        self.baudrate = baudrate
        self.serial_conn = None
        self.is_monitoring = False
        self.received_data = []
        self.monitor_thread = None
        
        logger.info(f"串口诊断工具已初始化: {port}@{baudrate}")
    
    def check_serial_availability(self):
        """检查串口可用性"""
        logger.info("=== 串口可用性检查 ===")
        
        if not SERIAL_AVAILABLE:
            logger.error("pyserial库未安装，无法进行串口通信")
            return False
        
        # 列出所有可用串口
        ports = serial.tools.list_ports.comports()
        logger.info(f"系统中发现 {len(ports)} 个串口:")
        
        target_port_found = False
        for port in ports:
            logger.info(f"  - {port.device}: {port.description}")
            if port.device == self.port:
                target_port_found = True
                logger.info(f"    ✅ 目标端口 {self.port} 已找到")
        
        if not target_port_found:
            logger.warning(f"⚠️ 目标端口 {self.port} 未找到")
        
        return target_port_found
    
    def test_serial_connection(self):
        """测试串口连接"""
        logger.info("=== 串口连接测试 ===")
        
        try:
            # 尝试打开串口
            logger.info(f"尝试打开串口 {self.port}...")
            self.serial_conn = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=1.0,
                write_timeout=1.0
            )
            
            logger.info(f"✅ 串口连接成功: {self.serial_conn}")
            logger.info(f"   - 端口: {self.serial_conn.port}")
            logger.info(f"   - 波特率: {self.serial_conn.baudrate}")
            logger.info(f"   - 超时: {self.serial_conn.timeout}秒")
            logger.info(f"   - 写超时: {self.serial_conn.write_timeout}秒")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 串口连接失败: {e}")
            return False
    
    def send_command_with_monitoring(self, command_hex, expected_response_hex, timeout=5.0):
        """发送命令并监控响应"""
        logger.info("=== 命令发送和响应监控 ===")
        
        if not self.serial_conn or not self.serial_conn.is_open:
            logger.error("串口未连接")
            return False
        
        try:
            # 清空接收缓冲区
            self.serial_conn.reset_input_buffer()
            self.received_data.clear()
            
            # 转换命令为字节
            command_bytes = bytes.fromhex(command_hex.replace(' ', ''))
            expected_bytes = bytes.fromhex(expected_response_hex.replace(' ', ''))
            
            logger.info(f"发送命令: {command_hex}")
            logger.info(f"命令字节: {command_bytes.hex().upper()}")
            logger.info(f"期望应答: {expected_response_hex}")
            logger.info(f"期望字节: {expected_bytes.hex().upper()}")
            
            # 开始监控
            self.start_monitoring()
            
            # 发送命令
            send_time = time.time()
            bytes_written = self.serial_conn.write(command_bytes)
            self.serial_conn.flush()
            
            logger.info(f"命令已发送: {bytes_written} 字节，耗时: {(time.time() - send_time)*1000:.1f}ms")
            
            # 等待响应
            logger.info(f"等待响应，超时时间: {timeout}秒...")
            start_wait = time.time()
            
            while time.time() - start_wait < timeout:
                if self.received_data:
                    # 检查是否收到期望的响应
                    for data_entry in self.received_data:
                        received_bytes = data_entry['data']
                        if received_bytes == expected_bytes:
                            elapsed = time.time() - send_time
                            logger.info(f"✅ 收到期望应答! 响应时间: {elapsed*1000:.1f}ms")
                            logger.info(f"应答数据: {received_bytes.hex().upper()}")
                            self.stop_monitoring()
                            return True
                
                time.sleep(0.01)  # 10ms检查间隔
            
            # 超时
            elapsed = time.time() - send_time
            logger.warning(f"⚠️ 等待应答超时 ({elapsed:.1f}秒)")
            
            # 显示所有接收到的数据
            if self.received_data:
                logger.info("接收到的所有数据:")
                for i, data_entry in enumerate(self.received_data):
                    logger.info(f"  数据包 {i+1}: {data_entry['data'].hex().upper()} (时间: {data_entry['timestamp']:.3f})")
            else:
                logger.warning("未接收到任何数据")
            
            self.stop_monitoring()
            return False
            
        except Exception as e:
            logger.error(f"命令发送异常: {e}")
            self.stop_monitoring()
            return False
    
    def start_monitoring(self):
        """开始数据监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_data, daemon=True)
        self.monitor_thread.start()
        logger.debug("数据监控已启动")
    
    def stop_monitoring(self):
        """停止数据监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        logger.debug("数据监控已停止")
    
    def _monitor_data(self):
        """数据监控线程"""
        logger.debug("监控线程已启动")
        
        while self.is_monitoring and self.serial_conn and self.serial_conn.is_open:
            try:
                if self.serial_conn.in_waiting > 0:
                    data = self.serial_conn.read(self.serial_conn.in_waiting)
                    timestamp = time.time()
                    
                    self.received_data.append({
                        'data': data,
                        'timestamp': timestamp,
                        'length': len(data)
                    })
                    
                    logger.debug(f"接收数据: {data.hex().upper()} ({len(data)} 字节)")
                
                time.sleep(0.001)  # 1ms检查间隔
                
            except Exception as e:
                logger.error(f"监控线程异常: {e}")
                break
        
        logger.debug("监控线程已退出")
    
    def close(self):
        """关闭连接"""
        self.stop_monitoring()
        
        if self.serial_conn and self.serial_conn.is_open:
            self.serial_conn.close()
            logger.info("串口连接已关闭")


def main():
    """主函数"""
    logger.info("开始串口通信深度诊断")
    
    # 创建诊断工具
    diagnostic = SerialDiagnosticTool('COM6', 115200)
    
    try:
        # 1. 检查串口可用性
        if not diagnostic.check_serial_availability():
            logger.error("串口可用性检查失败，退出诊断")
            return 1
        
        # 2. 测试串口连接
        if not diagnostic.test_serial_connection():
            logger.error("串口连接测试失败，退出诊断")
            return 1
        
        # 3. 测试IM948命令
        logger.info("开始测试IM948命令...")
        
        # 测试set_subscription命令
        success = diagnostic.send_command_with_monitoring(
            command_hex="49000B1205FF00041E010305C0000C4D",
            expected_response_hex="49000112134D",
            timeout=5.0
        )
        
        if success:
            logger.info("🎉 IM948通信测试成功!")
        else:
            logger.error("💥 IM948通信测试失败!")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        logger.info("用户中断诊断")
        return 0
    except Exception as e:
        logger.error(f"诊断过程异常: {e}")
        return 1
    finally:
        diagnostic.close()


if __name__ == "__main__":
    exit(main())
