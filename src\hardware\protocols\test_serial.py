#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
串口协议测试模块

提供测试串口通信功能的简单测试程序。
"""

import logging
import sys
import time
from typing import List, Tuple

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

# 导入协议类
try:
    from src.hardware.protocols.serial_protocol import SerialProtocol
    from src.hardware.protocol_base import ProtocolEventType
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保当前目录是项目根目录，或者已正确设置PYTHONPATH。")
    sys.exit(1)

logger = logging.getLogger("SerialTest")

def list_ports() -> None:
    """列出系统上可用的串口"""
    ports = SerialProtocol.list_available_ports()
    if not ports:
        print("未找到可用的串口设备")
        return
        
    print("\n可用串口设备:")
    print("-" * 80)
    print(f"{'端口名':<10} | {'描述':<40} | {'硬件ID':<30}")
    print("-" * 80)
    
    for port, desc, hwid in ports:
        print(f"{port:<10} | {desc[:40]:<40} | {hwid[:30]:<30}")

def on_data_received(data) -> None:
    """数据接收回调函数（增强版）"""
    if isinstance(data, bytes):
        # 字节数据处理
        hex_data = ' '.join([f'{b:02X}' for b in data])
        ascii_data = ''.join([chr(b) if 32 <= b <= 126 else '.' for b in data])
        logger.info(f"收到字节数据 (长度: {len(data)})")
        logger.info(f"  HEX: {hex_data}")
        logger.info(f"  ASCII: {ascii_data}")

        # 尝试解码为文本
        try:
            text_data = data.decode('utf-8').strip()
            if text_data:
                logger.info(f"  UTF-8: {text_data}")
        except UnicodeDecodeError:
            logger.debug("  无法解码为UTF-8文本")
    else:
        # 文本或数字数据
        logger.info(f"收到数据 (类型: {type(data).__name__}): {data}")

        # 如果是字符串，也显示十六进制
        if isinstance(data, str):
            try:
                hex_data = ' '.join([f'{ord(c):02X}' for c in data])
                logger.info(f"  HEX: {hex_data}")
            except:
                pass

def on_connected() -> None:
    """连接成功回调函数"""
    logger.info("串口已连接")

def on_disconnected() -> None:
    """断开连接回调函数"""
    logger.info("串口已断开连接")

def on_error(error) -> None:
    """错误回调函数"""
    logger.error(f"发生错误: {error}")

def hex_string_to_bytes(hex_str: str) -> bytes:
    """将十六进制字符串转换为字节数据"""
    # 移除空格和常见分隔符
    hex_str = hex_str.replace(' ', '').replace('-', '').replace(':', '').replace(',', '')

    # 确保长度为偶数
    if len(hex_str) % 2 != 0:
        hex_str = '0' + hex_str

    try:
        return bytes.fromhex(hex_str)
    except ValueError as e:
        raise ValueError(f"无效的十六进制字符串: {hex_str}") from e

def test_serial_connection(port: str = None, read_mode: str = "raw") -> None:
    """测试串口连接（增强版）"""
    if port is None:
        # 如果未指定端口，获取可用端口列表
        ports = SerialProtocol.list_available_ports()
        if not ports:
            logger.error("未找到可用的串口设备")
            return

        # 使用第一个可用端口
        port = ports[0][0]
        logger.info(f"使用自动选择的端口: {port}")

    # 创建串口协议实例
    serial_protocol = SerialProtocol(port)

    # 设置串口参数（使用原始模式以支持十六进制数据）
    serial_protocol.set_config({
        "baudrate": 9600,
        "bytesize": 8,
        "parity": "N",
        "stopbits": 1,
        "timeout": 1.0,
        "read_mode": read_mode  # 支持 "raw" 或 "line" 模式
    })
    
    # 注册事件回调
    serial_protocol.register_event_callback(ProtocolEventType.DATA_RECEIVED, on_data_received)
    serial_protocol.register_event_callback(ProtocolEventType.CONNECTED, on_connected)
    serial_protocol.register_event_callback(ProtocolEventType.DISCONNECTED, on_disconnected)
    serial_protocol.register_event_callback(ProtocolEventType.ERROR, on_error)
    
    # 连接到串口
    try:
        if serial_protocol.connect():
            logger.info(f"成功连接到串口 {port}，读取模式: {read_mode}")

            # 启动数据读取线程
            serial_protocol.start_reading()
            logger.info("数据读取线程已启动")

            # 交互式测试循环
            print("\n=== 串口通信交互测试 ===")
            print("命令选项:")
            print("1. 发送AT命令 (文本)")
            print("2. 发送十六进制数据")
            print("3. 监听数据 (10秒)")
            print("4. 退出测试")

            while True:
                try:
                    choice = input("\n请选择操作 (1-4): ").strip()

                    if choice == "1":
                        # 发送AT命令
                        print("发送AT命令...")
                        try:
                            response = serial_protocol.send_command("AT\r\n", wait_response=True, timeout=3.0)
                            if response is not None:
                                logger.info(f"收到AT响应: {response}")
                            else:
                                logger.warning("AT命令无响应")
                        except Exception as e:
                            logger.error(f"发送AT命令失败: {e}")

                    elif choice == "2":
                        # 发送十六进制数据
                        hex_input = input("请输入十六进制数据 (如: 53 53 53 或 535353): ").strip()
                        if hex_input:
                            try:
                                hex_bytes = hex_string_to_bytes(hex_input)
                                logger.info(f"准备发送十六进制数据: {hex_input} -> {hex_bytes.hex().upper()}")
                                serial_protocol.send_command(hex_bytes, wait_response=False)
                                logger.info("十六进制数据发送完成")
                            except Exception as e:
                                logger.error(f"发送十六进制数据失败: {e}")

                    elif choice == "3":
                        # 监听数据
                        print("开始监听数据 (10秒)，请从其他串口工具发送数据...")
                        time.sleep(10)
                        print("监听结束")

                    elif choice == "4":
                        # 退出测试
                        break

                    else:
                        print("无效选项，请重新选择")

                except KeyboardInterrupt:
                    print("\n用户中断测试")
                    break
                except Exception as e:
                    logger.error(f"操作异常: {e}")

            # 停止读取并断开连接
            serial_protocol.stop_reading()
            if serial_protocol.disconnect():
                logger.info("成功断开串口连接")
            else:
                logger.error("断开串口连接失败")

        else:
            logger.error(f"连接到串口 {port} 失败")

    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        
def main() -> None:
    """主函数（增强版）"""
    print("\n==== 串口通信测试程序 (十六进制增强版) ====\n")

    # 列出可用串口
    list_ports()

    # 用户选择测试模式
    print("\n请选择测试模式:")
    print("1. 使用自动选择的串口 (原始模式)")
    print("2. 手动指定串口 (原始模式)")
    print("3. 使用自动选择的串口 (行模式)")
    print("4. 手动指定串口 (行模式)")
    print("5. 退出程序")

    choice = input("请输入选项 (1-5): ")

    if choice == "1":
        test_serial_connection(read_mode="raw")
    elif choice == "2":
        port = input("请输入串口名称 (如 COM1 或 /dev/ttyUSB0): ")
        test_serial_connection(port, read_mode="raw")
    elif choice == "3":
        test_serial_connection(read_mode="line")
    elif choice == "4":
        port = input("请输入串口名称 (如 COM1 或 /dev/ttyUSB0): ")
        test_serial_connection(port, read_mode="line")
    elif choice == "5":
        print("程序已退出")
        return
    else:
        print("无效选项，程序已退出")
        return

if __name__ == "__main__":
    main() 