#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IM948惯性测量单元使用示例

演示如何使用十六进制协议配置框架与IM948设备进行通信，
包括设置订阅、获取欧拉角和三维位置数据的完整流程。

Author: Augment Agent
Date: 2025-08-01
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.hardware.protocols.hex_protocol_adapter import HexProtocolAdapter
from src.hardware.protocols.subscription_manager import SubscriptionManager
from src.hardware.protocols.data_stream_processor import DataStreamProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('im948_data.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


class IM948Controller:
    """
    IM948惯性测量单元控制器
    
    提供IM948设备的完整控制功能，包括：
    - 设备连接和初始化
    - 订阅管理和数据获取
    - 实时数据处理和回调
    """
    
    def __init__(self, config_path: str):
        """
        初始化IM948控制器
        
        Args:
            config_path: IM948配置文件路径
        """
        self.config_path = config_path
        
        # 初始化组件
        self.adapter = HexProtocolAdapter(config_path)
        self.subscription_manager = SubscriptionManager(max_data_per_subscription=1000)
        self.data_processor = DataStreamProcessor(max_buffer_size=1000)
        
        # 数据存储
        self.euler_data = []
        self.position_data = []
        
        logger.info("IM948控制器已初始化")
    
    def connect(self, max_retries: int = 3) -> bool:
        """
        连接IM948设备（带重试机制）

        Args:
            max_retries: 最大重试次数

        Returns:
            连接结果
        """
        for attempt in range(max_retries):
            try:
                logger.info(f"尝试连接IM948设备 (第{attempt + 1}/{max_retries}次)")

                if self.adapter.connect():
                    logger.info("IM948设备连接成功")
                    return True
                else:
                    logger.warning(f"IM948设备连接失败 (第{attempt + 1}次尝试)")
                    if attempt < max_retries - 1:
                        time.sleep(2)  # 等待2秒后重试

            except Exception as e:
                logger.error(f"连接IM948设备异常 (第{attempt + 1}次尝试): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # 等待2秒后重试

        logger.error(f"IM948设备连接失败，已尝试{max_retries}次")
        return False
    
    def disconnect(self):
        """断开设备连接"""
        try:
            # 停止所有订阅
            self.stop_all_subscriptions()
            
            # 断开设备连接
            self.adapter.disconnect()
            
            # 清理资源
            self.subscription_manager.stop_all_subscriptions()
            self.data_processor.cleanup()
            
            logger.info("IM948设备已断开连接")
            
        except Exception as e:
            logger.error(f"断开IM948设备异常: {e}")
    
    def get_device_info(self) -> dict:
        """
        获取设备信息
        
        Returns:
            设备信息字典
        """
        try:
            result = self.adapter.execute_command('get_device_info')
            if result and result.get('success'):
                return result.get('data', {})
            else:
                logger.warning("获取设备信息失败")
                return {}
                
        except Exception as e:
            logger.error(f"获取设备信息异常: {e}")
            return {}
    
    def setup_subscription(self) -> bool:
        """
        设置功能订阅

        Returns:
            设置结果
        """
        try:
            result = self.adapter.execute_command('set_subscription')
            if result and result.get('success'):
                logger.info("功能订阅设置成功")
                return True
            else:
                logger.error("功能订阅设置失败")
                return False

        except Exception as e:
            logger.error(f"设置功能订阅异常: {e}")
            return False

    def start_data_subscription(self) -> bool:
        """
        启动数据订阅（开始自动上报）

        Returns:
            启动结果
        """
        try:
            result = self.adapter.execute_command('start_subscription')
            if result and result.get('success'):
                logger.info("数据订阅启动成功")
                return True
            else:
                logger.error("数据订阅启动失败")
                return False

        except Exception as e:
            logger.error(f"启动数据订阅异常: {e}")
            return False
    
    def start_data_stream_subscription(self, callback=None) -> bool:
        """
        启动数据流订阅（欧拉角和位置数据）

        Args:
            callback: 数据回调函数

        Returns:
            启动结果
        """
        try:
            # 设置数据回调
            if callback is None:
                callback = self._data_stream_callback

            # 启动订阅
            success = self.adapter.start_subscription('get_euler_angles', callback)
            if success:
                logger.info("数据流订阅已启动（包含欧拉角和位置数据）")
            else:
                logger.error("数据流订阅启动失败")

            return success

        except Exception as e:
            logger.error(f"启动数据流订阅异常: {e}")
            return False
    
    def stop_subscription(self, subscription_name: str) -> bool:
        """
        停止指定订阅
        
        Args:
            subscription_name: 订阅名称
            
        Returns:
            停止结果
        """
        try:
            success = self.adapter.stop_subscription(subscription_name)
            if success:
                logger.info(f"订阅 {subscription_name} 已停止")
            else:
                logger.warning(f"停止订阅 {subscription_name} 失败")
            
            return success
            
        except Exception as e:
            logger.error(f"停止订阅异常: {e}")
            return False
    
    def stop_all_subscriptions(self) -> bool:
        """
        停止所有订阅
        
        Returns:
            停止结果
        """
        try:
            # 停止设备端订阅
            result = self.adapter.execute_command('stop_subscription')
            if result and result.get('success'):
                logger.info("设备端订阅停止成功")
            else:
                logger.warning("设备端订阅停止失败，但继续清理本地资源")

            # 停止本地订阅
            self.adapter.stop_all_subscriptions()

            logger.info("所有订阅已停止")
            return True

        except Exception as e:
            logger.error(f"停止所有订阅异常: {e}")
            # 即使出现异常也要尝试清理本地资源
            try:
                self.adapter.stop_all_subscriptions()
            except Exception as cleanup_error:
                logger.error(f"清理本地订阅资源异常: {cleanup_error}")
            return False
    
    def get_latest_euler_data(self, count: int = 10) -> list:
        """
        获取最新的欧拉角数据
        
        Args:
            count: 获取数据数量
            
        Returns:
            欧拉角数据列表
        """
        return self.adapter.get_subscription_data('get_euler_angles', count)
    
    def get_latest_position_data(self, count: int = 10) -> list:
        """
        获取最新的位置数据
        
        Args:
            count: 获取数据数量
            
        Returns:
            位置数据列表
        """
        return self.adapter.get_subscription_data('get_position_3d', count)
    
    def get_subscription_status(self) -> dict:
        """
        获取订阅状态
        
        Returns:
            订阅状态信息
        """
        return self.adapter.get_subscription_status()
    
    def _data_stream_callback(self, data: dict):
        """
        数据流回调函数（处理欧拉角和位置数据）

        Args:
            data: 包含欧拉角和位置的数据
        """
        try:
            # 提取欧拉角数据
            roll = data.get('roll', {}).get('value', 0.0) if isinstance(data.get('roll'), dict) else data.get('roll', 0.0)
            pitch = data.get('pitch', {}).get('value', 0.0) if isinstance(data.get('pitch'), dict) else data.get('pitch', 0.0)
            yaw = data.get('yaw', {}).get('value', 0.0) if isinstance(data.get('yaw'), dict) else data.get('yaw', 0.0)

            # 提取位置数据
            x = data.get('x', {}).get('value', 0.0) if isinstance(data.get('x'), dict) else data.get('x', 0.0)
            y = data.get('y', {}).get('value', 0.0) if isinstance(data.get('y'), dict) else data.get('y', 0.0)
            z = data.get('z', {}).get('value', 0.0) if isinstance(data.get('z'), dict) else data.get('z', 0.0)

            # 提取时间戳
            timestamp = data.get('timestamp')

            # 存储欧拉角数据
            self.euler_data.append({
                'roll': roll,
                'pitch': pitch,
                'yaw': yaw,
                'timestamp': timestamp
            })

            # 存储位置数据
            self.position_data.append({
                'x': x,
                'y': y,
                'z': z,
                'timestamp': timestamp
            })

            # 限制数据数量
            if len(self.euler_data) > 1000:
                self.euler_data.pop(0)
            if len(self.position_data) > 1000:
                self.position_data.pop(0)

            logger.debug(f"数据流: Roll={roll:.2f}°, Pitch={pitch:.2f}°, Yaw={yaw:.2f}°, X={x:.3f}m, Y={y:.3f}m, Z={z:.3f}m")

        except Exception as e:
            logger.error(f"数据流回调异常: {e}")


def validate_config():
    """验证配置文件"""
    config_path = Path(__file__).parent / "im948_config.json"

    if not config_path.exists():
        logger.error(f"配置文件不存在: {config_path}")
        return False

    try:
        # 测试配置文件加载
        from src.hardware.protocols.protocol_config_parser import ProtocolConfigParser
        parser = ProtocolConfigParser()

        with open(config_path, 'r', encoding='utf-8') as f:
            import json
            config = json.load(f)

        # 验证配置文件格式
        if parser.validate_config(config):
            logger.info("✅ 配置文件格式验证通过")
        else:
            logger.error("❌ 配置文件格式验证失败")
            return False

        # 验证关键指令
        commands = config.get('protocol', {}).get('commands', {})
        required_commands = ['set_subscription', 'start_subscription', 'get_euler_angles']

        for cmd in required_commands:
            if cmd in commands:
                logger.info(f"✅ 指令 '{cmd}' 配置正确")
            else:
                logger.error(f"❌ 缺少必需指令: {cmd}")
                return False

        # 验证数据格式配置
        euler_config = commands.get('get_euler_angles', {})
        response_fields = euler_config.get('response', {}).get('fields', {})

        expected_fields = ['roll', 'pitch', 'yaw', 'x', 'y', 'z']
        for field in expected_fields:
            if field in response_fields:
                field_config = response_fields[field]
                if field_config.get('type') == 'int16_le':
                    logger.info(f"✅ 字段 '{field}' 数据类型配置正确 (int16_le)")
                else:
                    logger.warning(f"⚠️ 字段 '{field}' 数据类型: {field_config.get('type')}")
            else:
                logger.error(f"❌ 缺少必需字段: {field}")
                return False

        logger.info("✅ 配置文件验证完成，所有检查通过")
        return True

    except Exception as e:
        logger.error(f"❌ 配置文件验证异常: {e}")
        return False


def main():
    """主函数"""
    # 配置文件路径
    config_path = Path(__file__).parent / "im948_config.json"

    if not config_path.exists():
        logger.error(f"配置文件不存在: {config_path}")
        return

    # 验证配置文件
    logger.info("=== 配置文件验证 ===")
    if not validate_config():
        logger.error("配置文件验证失败，程序退出")
        return
    
    # 创建IM948控制器
    controller = IM948Controller(str(config_path))
    
    try:
        # 连接设备
        if not controller.connect():
            logger.error("设备连接失败，退出程序")
            return
        
        # 步骤1：设置功能订阅
        logger.info("步骤1：设置功能订阅...")
        if not controller.setup_subscription():
            logger.error("功能订阅设置失败，退出程序")
            return

        # 步骤2：启动数据订阅（开始自动上报）
        logger.info("步骤2：启动数据订阅...")
        if not controller.start_data_subscription():
            logger.error("数据订阅启动失败，退出程序")
            return

        # 步骤3：启动数据流监听
        logger.info("步骤3：启动数据流监听...")
        if not controller.start_data_stream_subscription():
            logger.error("数据流订阅启动失败，退出程序")
            return
        
        # 运行数据采集
        logger.info("开始连续数据流采集，按Ctrl+C停止...")

        start_time = time.time()
        data_count = 0

        last_data_time = time.time()
        no_data_warning_count = 0

        while True:
            time.sleep(1.0)

            try:
                # 检查连接状态
                if not controller.adapter.is_connected():
                    logger.error("设备连接丢失，尝试重新连接...")
                    if controller.connect():
                        logger.info("重新连接成功，重新启动订阅...")
                        controller.setup_subscription()
                        controller.start_data_subscription()
                        controller.start_data_stream_subscription()
                    else:
                        logger.error("重新连接失败，程序退出")
                        break

                # 显示订阅状态
                status = controller.get_subscription_status()

                # 统计数据接收情况
                current_euler_count = len(controller.euler_data)

                if current_euler_count > data_count:
                    new_data_count = current_euler_count - data_count
                    data_count = current_euler_count
                    last_data_time = time.time()
                    no_data_warning_count = 0
                    logger.info(f"数据流状态: 活跃订阅={len(status)}, 新接收数据={new_data_count}帧, 总数据={data_count}帧")
                else:
                    # 检查是否长时间没有数据
                    if time.time() - last_data_time > 10:  # 10秒没有新数据
                        no_data_warning_count += 1
                        if no_data_warning_count <= 3:
                            logger.warning(f"数据流状态: 活跃订阅={len(status)}, 已{time.time() - last_data_time:.0f}秒未收到新数据")
                    else:
                        logger.info(f"数据流状态: 活跃订阅={len(status)}, 等待数据中...")

                # 显示最新数据
                if controller.euler_data and controller.position_data:
                    euler_data = controller.euler_data[-1]
                    position_data = controller.position_data[-1]

                    logger.info(f"最新数据 - 欧拉角: Roll={euler_data.get('roll', 0):.2f}°, "
                              f"Pitch={euler_data.get('pitch', 0):.2f}°, "
                              f"Yaw={euler_data.get('yaw', 0):.2f}° | "
                              f"位置: X={position_data.get('x', 0):.3f}m, "
                              f"Y={position_data.get('y', 0):.3f}m, "
                              f"Z={position_data.get('z', 0):.3f}m")

                # 运行10分钟后自动停止
                if time.time() - start_time > 600:
                    logger.info("运行时间达到10分钟，自动停止")
                    break

            except Exception as loop_error:
                logger.error(f"主循环异常: {loop_error}")
                time.sleep(1)  # 出现异常时短暂等待
    
    except KeyboardInterrupt:
        logger.info("用户中断，停止数据采集")
    
    except Exception as e:
        logger.error(f"程序运行异常: {e}")
    
    finally:
        # 清理资源
        controller.disconnect()
        logger.info("程序结束")


if __name__ == "__main__":
    main()
