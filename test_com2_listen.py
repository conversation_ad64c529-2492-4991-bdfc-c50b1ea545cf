#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
COM2监听测试脚本
只监听COM2，配合外部工具从COM3发送数据
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.hardware.protocols.serial_protocol import SerialProtocol
from src.hardware.protocol_base import ProtocolEventType

def on_data_received(data):
    """数据接收回调"""
    if isinstance(data, bytes):
        hex_data = ' '.join([f'{b:02X}' for b in data])
        ascii_data = ''.join([chr(b) if 32 <= b <= 126 else '.' for b in data])
        print(f"\n🎉 [接收成功] 长度: {len(data)} 字节")
        print(f"    HEX:   {hex_data}")
        print(f"    ASCII: {ascii_data}")
        
        # 尝试解码为UTF-8
        try:
            utf8_data = data.decode('utf-8')
            print(f"    UTF-8: {utf8_data}")
        except UnicodeDecodeError:
            print(f"    UTF-8: (无法解码)")
    else:
        print(f"\n🎉 [接收成功] 数据: {data} (类型: {type(data).__name__})")

def test_com2_listen():
    """测试COM2监听"""
    print("=== COM2 监听测试 ===")
    print("注意: 请确保COM3没有被其他程序占用，或者使用外部工具从COM3发送数据")
    
    # 创建COM2实例
    com2 = SerialProtocol("COM2")
    
    # 配置串口参数
    com2.set_config({
        "baudrate": 9600,
        "bytesize": 8,
        "parity": "N",
        "stopbits": 1,
        "timeout": 0.1,
        "read_mode": "raw"
    })
    
    # 注册回调
    com2.register_event_callback(ProtocolEventType.DATA_RECEIVED, on_data_received)
    com2.register_event_callback(ProtocolEventType.CONNECTED, lambda: print("🔗 COM2已连接"))
    com2.register_event_callback(ProtocolEventType.DISCONNECTED, lambda: print("🔌 COM2已断开"))
    com2.register_event_callback(ProtocolEventType.ERROR, lambda e: print(f"❌ COM2错误: {e}"))
    
    try:
        # 连接COM2
        if not com2.connect():
            print("❌ COM2连接失败")
            return
        
        # 启动读取线程
        if not com2.start_reading():
            print("❌ 启动读取线程失败")
            return
        
        print("✅ COM2监听已启动")
        print("\n" + "="*60)
        print("现在请从COM3发送数据，程序将显示接收到的内容")
        print("支持的数据格式:")
        print("  - 十六进制: 53 53 53")
        print("  - 文本: Hello World")
        print("  - 任意字节数据")
        print("="*60)
        
        # 持续监听
        try:
            while True:
                print(f"\r⏰ 监听中... {time.strftime('%H:%M:%S')}", end='', flush=True)
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n⏹️ 用户中断监听")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        print("\n=== 清理资源 ===")
        try:
            com2.stop_reading()
            com2.disconnect()
            print("✅ 资源清理完成")
        except Exception as e:
            print(f"⚠️ 清理资源时出现异常: {e}")

if __name__ == "__main__":
    test_com2_listen()
