# IM948惯性测量单元使用指南

## 概述

本示例演示如何使用十六进制协议配置框架与IM948惯性测量单元进行通信，实现欧拉角和三维位置数据的订阅和获取。

## 文件说明

- `im948_config.json` - IM948协议配置文件
- `im948_example.py` - IM948使用示例代码
- `README_IM948.md` - 本使用说明文档

## 协议特性

### IM948协议格式

IM948使用自定义的十六进制协议格式：

```
起始码(49) + 地址(00) + 长度 + 数据体 + 校验和 + 结束码(4D)
```

### 应答验证机制

框架支持完整的应答验证功能，确保命令执行成功：

- **期望应答配置**: 每个一次性命令都可以配置期望的应答数据
- **精确匹配验证**: 字节级精确匹配，确保应答数据完全正确
- **自动重试机制**: 应答不匹配时自动重试，提高通信可靠性
- **详细错误报告**: 提供详细的验证失败信息和统计数据

#### 应答验证示例

```json
{
  "set_subscription": {
    "type": "one_time",
    "response": {
      "expected_response": "49 00 01 12 13 4D",
      "template": "49 00 01 12 13 4D",
      "data_parsing": {
        "success_condition": "exact_match"
      }
    }
  }
}
```

### 支持的指令类型

1. **一次性指令 (one_time)**

   - `set_subscription` - 设置功能订阅（订阅欧拉角和位置数据）
   - `start_subscription` - 启动数据订阅（开始自动上报数据）
   - `stop_subscription` - 停止所有订阅
   - `get_device_info` - 获取设备信息

2. **持续指令 (continuous)**

   - `get_euler_angles` - 获取欧拉角和位置数据流（合并数据流）

### 工作流程

1. **连接设备** - 建立串口连接
2. **设置订阅** - 发送订阅指令配置要获取的数据类型
3. **启动订阅** - 发送启动指令开始自动上报数据
4. **持续监听** - 在主循环中持续接收和解析数据

### 数据格式

#### 合并数据流（欧拉角 + 位置数据）

IM948设备在一个数据包中同时返回欧拉角和位置数据：

```json
{
  "subscription_mask": 192,    // 订阅掩码 (0x00C0, bit6+bit7)
  "timestamp": 101007663,      // 时间戳 (ms)
  "roll": -11.568604,          // 横滚角 (度)
  "pitch": -1.362305,          // 俯仰角 (度)
  "yaw": -99.854736,           // 偏航角 (度)
  "x": 0.000,                  // X轴位置 (米)
  "y": 0.000,                  // Y轴位置 (米)
  "z": 0.000                   // Z轴位置 (米)
}
```

#### 数据类型和缩放

- **欧拉角**: S16小端格式，缩放因子 0.0054931640625
- **位置数据**: S16小端格式，缩放因子 0.001 (除以1000)
- **时间戳**: U32小端格式
- **订阅掩码**: U16小端格式

## 协议指令详解

### 指令格式

#### 1. 订阅指令
```
发送: 49 00 0B 12 05 FF 00 04 1E 01 03 05 C0 00 0C 4D
说明: 设置功能订阅，启用欧拉角(bit6)和三维位置(bit7)数据输出
```

#### 2. 启动指令
```
发送: 49 00 01 19 1A 4D
说明: 启动数据订阅，开始自动上报数据
```

#### 3. 典型响应
```
接收: 49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D
说明: 包含欧拉角和位置数据的响应报文
```

### 报文解析

以典型响应为例：`49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D`

| 字节位置 | 数据 | 说明 |
|---------|------|------|
| 0 | 49 | 起始码 |
| 1 | 00 | 地址 |
| 2 | 13 | 数据体长度(19字节) |
| 3 | 11 | 功能标签 |
| 4-5 | C0 00 | 订阅掩码(小端): 0x00C0 |
| 6-9 | 2F 41 05 06 | 时间戳(小端): 0x0605412F |
| 10-11 | C6 F7 | Roll(小端): 0xF7C6 = -2106 |
| 12-13 | 08 FF | Pitch(小端): 0xFF08 = -248 |
| 14-15 | FE B8 | Yaw(小端): 0xB8FE = -18178 |
| 16-17 | 00 00 | X位置(小端): 0x0000 = 0 |
| 18-19 | 00 00 | Y位置(小端): 0x0000 = 0 |
| 20-21 | 00 00 | Z位置(小端): 0x0000 = 0 |
| 22 | D9 | 校验和 |
| 23 | 4D | 结束码 |

**数据转换示例：**
* **Roll**: -2106 × 0.0054931640625 = **-11.57°**
* **Pitch**: -248 × 0.0054931640625 = **-1.36°**
* **Yaw**: -18178 × 0.0054931640625 = **-99.85°**
* **X**: 0 / 1000.0 = **0.0 m**
* **Y**: 0 / 1000.0 = **0.0 m**
* **Z**: 0 / 1000.0 = **0.0 m**
  (这很正常，通常位置数据是相对开机后的位移，初始为0)

## 使用方法

### 1. 环境准备

确保已安装必要的依赖：

```bash
pip install pyserial
```

### 2. 硬件连接

1. 将IM948设备连接到计算机的串口
2. 确认串口号（如COM2）
3. 修改配置文件中的串口设置

### 3. 配置文件修改

编辑 `im948_config.json` 文件：

```json
{
  "connection": {
    "port": "COM2",        // 修改为实际串口号
    "baudrate": 115200,    // 波特率
    "timeout": 1.0         // 超时时间
  }
}
```

### 4. 运行示例

```bash
cd examples
python im948_example.py
```

### 5. 程序输出

程序运行后会显示：

- 配置文件验证结果
- 设备连接状态
- 订阅设置和启动过程
- 连续数据流监控
- 实时数据更新

示例输出：

```
2025-08-02 16:02:12 - __main__ - INFO - === 配置文件验证 ===
2025-08-02 16:02:12 - __main__ - INFO - ✅ 配置文件格式验证通过
2025-08-02 16:02:12 - __main__ - INFO - ✅ 指令 'set_subscription' 配置正确
2025-08-02 16:02:12 - __main__ - INFO - ✅ 指令 'start_subscription' 配置正确
2025-08-02 16:02:12 - __main__ - INFO - IM948设备连接成功
2025-08-02 16:02:12 - __main__ - INFO - 步骤1：设置功能订阅...
2025-08-02 16:02:12 - __main__ - INFO - 功能订阅设置成功
2025-08-02 16:02:12 - __main__ - INFO - 步骤2：启动数据订阅...
2025-08-02 16:02:12 - __main__ - INFO - 数据订阅启动成功
2025-08-02 16:02:12 - __main__ - INFO - 步骤3：启动数据流订阅...
2025-08-02 16:02:12 - __main__ - INFO - 数据流订阅已启动（包含欧拉角和位置数据）
2025-08-02 16:02:12 - __main__ - INFO - 开始连续数据流采集，按Ctrl+C停止...
2025-08-02 16:02:13 - __main__ - INFO - 数据流状态: 活跃订阅=1, 新接收数据=5帧, 总数据=5帧
2025-08-02 16:02:13 - __main__ - INFO - 最新数据 - 欧拉角: Roll=-11.57°, Pitch=-1.36°, Yaw=-99.85° | 位置: X=0.000m, Y=0.000m, Z=0.000m
```

## 高级用法

### 应答验证功能

框架提供了完整的应答验证功能，确保命令执行成功：

```python
# 获取验证统计信息
validation_stats = controller.adapter.get_validation_stats()
print(f"验证成功次数: {validation_stats['validation_successes']}")
print(f"验证失败次数: {validation_stats['validation_failures']}")
print(f"验证成功率: {validation_stats['validation_success_rate']:.2%}")

# 检查最后的验证错误
if validation_stats['last_validation_error']:
    print(f"最后验证错误: {validation_stats['last_validation_error']}")
```

#### 应答验证工作原理

1. **命令发送**: 发送一次性命令到设备
2. **应答接收**: 等待设备返回应答数据
3. **精确匹配**: 将接收到的应答与期望应答进行字节级比较
4. **结果判断**: 匹配成功则命令执行成功，否则重试或报错

#### 验证失败处理

```python
try:
    # 发送命令（自动进行应答验证）
    result = controller.set_subscription()
    if result:
        print("订阅设置成功")
    else:
        print("订阅设置失败")
except Exception as e:
    print(f"命令执行异常: {e}")

    # 检查验证统计
    stats = controller.adapter.get_validation_stats()
    if stats['validation_failures'] > 0:
        print("可能的原因：设备应答与期望不匹配")
```

### 自定义数据回调

```python
def custom_data_callback(data):
    """自定义数据流处理"""
    # 提取欧拉角数据
    roll = data.get('roll', 0.0)
    pitch = data.get('pitch', 0.0)
    yaw = data.get('yaw', 0.0)

    # 提取位置数据
    x = data.get('x', 0.0)
    y = data.get('y', 0.0)
    z = data.get('z', 0.0)

    # 提取时间戳
    timestamp = data.get('timestamp')

    # 自定义处理逻辑
    print(f"[{timestamp}] 姿态: R={roll:.1f}°, P={pitch:.1f}°, Y={yaw:.1f}° | 位置: X={x:.2f}m, Y={y:.2f}m, Z={z:.2f}m")

# 使用自定义回调
controller.start_data_stream_subscription(callback=custom_data_callback)
```

### 数据分析

```python
# 获取历史数据
euler_history = controller.get_latest_euler_data(100)
position_history = controller.get_latest_position_data(100)

# 计算欧拉角统计信息
import statistics
if euler_history:
    rolls = [data['roll'] for data in euler_history]
    pitches = [data['pitch'] for data in euler_history]
    yaws = [data['yaw'] for data in euler_history]

    print(f"欧拉角统计 (最近{len(euler_history)}帧):")
    print(f"  Roll: 平均={statistics.mean(rolls):.2f}°, 标准差={statistics.stdev(rolls):.2f}°")
    print(f"  Pitch: 平均={statistics.mean(pitches):.2f}°, 标准差={statistics.stdev(pitches):.2f}°")
    print(f"  Yaw: 平均={statistics.mean(yaws):.2f}°, 标准差={statistics.stdev(yaws):.2f}°")

# 计算位置统计信息
if position_history:
    xs = [data['x'] for data in position_history]
    ys = [data['y'] for data in position_history]
    zs = [data['z'] for data in position_history]

    print(f"位置统计 (最近{len(position_history)}帧):")
    print(f"  X: 平均={statistics.mean(xs):.3f}m, 标准差={statistics.stdev(xs):.3f}m")
    print(f"  Y: 平均={statistics.mean(ys):.3f}m, 标准差={statistics.stdev(ys):.3f}m")
    print(f"  Z: 平均={statistics.mean(zs):.3f}m, 标准差={statistics.stdev(zs):.3f}m")
```

### 订阅管理

```python
# 获取订阅状态
status = controller.get_subscription_status()
print(f"活跃订阅数量: {len(status)}")

# 停止数据流订阅
controller.stop_data_stream_subscription()

# 停止所有订阅
controller.stop_all_subscriptions()

# 检查连接状态
if controller.adapter.is_connected():
    print("设备连接正常")
else:
    print("设备连接断开")
```

## 协议配置详解

### 指令配置结构

```json
{
  "command_name": {
    "type": "continuous",           // 指令类型
    "description": "指令描述",
    "subscription_config": {        // 订阅配置（仅持续指令）
      "max_data_count": 1000,      // 最大数据缓存数量
      "data_interval": 0.01,       // 数据间隔（秒）
      "auto_start": false          // 是否自动启动
    },
    "response": {                   // 响应配置
      "template": "A5 5A ...",     // 响应模板
      "fields": {                  // 数据字段定义
        "field_name": {
          "type": "int16_be",      // 数据类型
          "scale": 0.01,           // 缩放因子
          "unit": "度",            // 单位
          "description": "字段描述"
        }
      },
      "data_parsing": {            // 数据解析配置
        "validation": {            // 数据验证
          "field_name": {"min": -180.0, "max": 180.0}
        },
        "unit_conversion": {       // 单位转换
          "field_name": {"from": "度", "to": "弧度", "factor": 0.0174533}
        }
      }
    }
  }
}
```

### 支持的数据类型

- `uint8` - 8位无符号整数
- `int8` - 8位有符号整数
- `uint16_be/le` - 16位无符号整数（大端/小端）
- `int16_be/le` - 16位有符号整数（大端/小端）
- `uint32_be/le` - 32位无符号整数（大端/小端）
- `int32_be/le` - 32位有符号整数（大端/小端）
- `float32_be/le` - 32位浮点数（大端/小端）

### 校验和算法

- `sum8` - 8位累加和
- `xor` - 异或校验
- `crc16` - CRC16校验
- `cs` - CS校验和（256-sum）
- `modbus_crc` - Modbus CRC16

## 报文解析


**报文原文:**
49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D

---

### 1. 整体报文结构分析

**首先，我们将报文拆分成协议定义的各个部分。**

| **部分**   | **十六进制值**                                               | **说明**                                                                                       |
| ---------------- | ------------------------------------------------------------------ | ---------------------------------------------------------------------------------------------------- |
| **起始码** | **49**                                                       | **帧开始，正确。**                                                                             |
| **地址**   | **00**                                                       | **模块地址为0。**                                                                              |
| **长度**   | **13**                                                       | **数据体**的长度。**0x13** **转换为十进制是** **19**，表示数据体有19个字节。 |
| **数据体** | **11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00** | **19字节的数据内容。**                                                                         |
| **校验码** | **D9**                                                       | **经验证，从地址到数据体所有字节的和**0x5D9**，取低8位为**0xD9**，校验正确。**           |
| **结束码** | **4D**                                                       | **帧结束，正确。**                                                                             |

---

### 2. 数据体详细解析 (核心部分)

**数据体是这条报文的灵魂，我们根据“主动上报”的格式来解析这19个字节。**

| **字节范围** | **十六进制值**  | **功能说明**                                                                                                                                                                        |
| ------------------ | --------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **字节 1**   | **11**          | **功能标签**: 代表这是一帧“回复或主动上报”的数据。                                                                                                                                |
| **字节 2-3** | **C0 00**       | **功能订阅标识**: **0x00C0** **(小端)。二进制为**...1100 0000**，表示订阅了**bit6**和**bit7**。查表可知，这正是**欧拉角**和**惯导三维位置**。** |
| **字节 4-7** | **2F 41 05 06** | **时间戳**: **0x0605412F** **(小端)。转换为十进制为** **100,999,471毫秒**。                                                                                       |

---

#### 订阅数据解析

**接下来的数据会按照订阅标识中比特位的顺序排列。**bit6 **(欧拉角) 在前，**bit7 **(位置) 在后。**

* **数据长度**: 3个S16类型，共6个字节。
* **对应报文字节**: **C6 F7 08 FF FE B8**
* **解析**:

  * **X轴 (Roll/滚转角)**: **C6 F7** **->** **0xF7C6** **(小端) =** **-2106**
  * **Y轴 (Pitch/俯仰角)**: **08 FF** **->** **0xFF08** **(小端) =** **-248**
  * **Z轴 (Yaw/偏航角)**: **FE B8** **->** **0xB8FE** **(小端) =** **-18178**
* **计算物理值 (乘以系数** **0.0054931640625**)**:**

  * **Roll**: -2106 * 0.005493... ≈ **-11.57 °**
  * **Pitch**: -248 * 0.005493... ≈ **-1.36 °**
  * **Yaw**: -18178 * 0.005493... ≈ **-99.87 °**
* **数据长度**: 3个S16类型，共6个字节。
* **对应报文字节**: **00 00 00 00 00 00**
* **解析**:

  * **X轴位置**: **00 00** **->** **0**
  * **Y轴位置**: **00 00** **->** **0**
  * **Z轴位置**: **00 00** **->** **0**
* **计算物理值 (除以** **1000.0**)**:**

  * **X**: 0 / 1000.0 = **0.0 m**
* **Y**: 0 / 1000.0 = **0.0 m**
* **Z**: 0 / 1000.0 = **0.0 m**
  (这很正常，通常位置数据是相对开机后的位移，初始为0)

---

### 最终解析结果汇总

* **模块地址**: 0
* **报文类型**: 包含欧拉角和位置的主动上报数据
* **时间戳**: 100,999,471 ms
* **姿态数据 (欧拉角)**:

  * **滚转角 (Roll)**: **-11.57°**
* **俯仰角 (Pitch)**: **-1.36°**
* **偏航角 (Yaw)**: **-99.87°**
* **位置数据**:

  * **X轴位移**: **0.0 m**
* **Y轴位移**: **0.0 m**
* **Z轴位移**: **0.0 m**

## 故障排除

### 常见问题

1. **设备连接失败**

   - 检查串口号是否正确
   - 确认设备是否正确连接
   - 检查串口是否被其他程序占用
2. **数据接收异常**

   - 检查波特率设置
   - 确认协议配置是否正确
   - 检查设备是否支持订阅功能
3. **数据解析错误**

   - 检查数据模板是否匹配实际数据格式
   - 确认字节序（大端/小端）设置
   - 检查校验和算法是否正确

### 调试方法

1. **启用详细日志**

```python
logging.basicConfig(level=logging.DEBUG)
```

2. **查看原始数据**

```python
# 在回调函数中打印原始数据
def debug_callback(data):
    print(f"原始数据: {data}")
```

3. **检查订阅状态**

```python
status = controller.get_subscription_status()
print(f"订阅状态: {status}")
```

## 测试验证

### 配置文件验证测试

运行配置文件验证测试：

```bash
python examples/test_im948_commands.py
```

预期输出：
```
=== IM948指令生成测试 ===
✅ 订阅指令生成正确
✅ 启动指令生成正确
✅ 响应模板解析正确
```

### 数据解析测试

运行数据解析测试：

```bash
python examples/test_im948_data_parsing.py
```

预期输出：
```
=== IM948数据解析测试 ===
✅ 校验和验证通过
✅ 欧拉角数据解析正确
✅ 位置数据解析正确
✅ 数据范围验证通过
```

### 完整功能测试

运行完整示例程序：

```bash
python examples/im948_example.py
```

**注意事项：**
- 确保串口设备可用（真实IM948设备或虚拟串口）
- 检查串口号配置是否正确
- 如果使用虚拟串口测试，可能会出现超时，这是正常现象
- 程序会自动生成日志文件 `im948_data.log`

## 故障排除

### 常见问题

1. **串口连接失败**
   - 检查串口号是否正确
   - 确认设备是否被其他程序占用
   - 检查串口权限

2. **指令发送超时**
   - 检查设备是否正常工作
   - 确认波特率设置正确
   - 检查硬件连接

3. **数据解析错误**
   - 确认协议格式是否匹配
   - 检查数据长度和校验和
   - 验证字节序设置

4. **依赖库缺失**
   ```bash
   pip install pyserial
   # 可选：安装jsonschema用于配置验证
   pip install jsonschema
   ```

### 调试技巧

1. **启用详细日志**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **查看原始数据**
   ```python
   # 在数据回调中打印原始字节
   def debug_callback(data):
       print(f"原始数据: {data}")
   ```

3. **检查配置文件**
   ```bash
   python -c "import json; print(json.load(open('examples/im948_config.json')))"
   ```

## 扩展开发

### 添加新指令

1. 在配置文件中添加新指令定义
2. 根据需要选择指令类型（one_time/continuous）
3. 配置请求和响应模板
4. 在代码中调用相应方法

### 自定义数据处理

1. 实现自定义数据解析器
2. 注册到数据流处理器
3. 配置数据过滤和转换规则

## 参考资料

- [IM948协议文档](https://www.yuque.com/cxqwork/lkw3sg/yqa3e0?#KjO2V)
- [十六进制协议配置框架文档](../src/hardware/protocols/README.md)
- [PySerial文档](https://pyserial.readthedocs.io/)
