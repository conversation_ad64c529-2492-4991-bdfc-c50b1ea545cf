#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
订阅管理器模块

提供持续数据流的订阅管理功能，支持异步数据接收和回调机制。
用于处理工业自动化协议中的持续数据流指令。

Author: Augment Agent
Date: 2025-08-01
"""

import logging
import threading
import time
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime
from collections import defaultdict, deque
from enum import Enum

# 获取logger
logger = logging.getLogger(__name__)


class SubscriptionStatus(Enum):
    """订阅状态枚举"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


class SubscriptionManager:
    """
    订阅管理器
    
    提供持续数据流的订阅管理功能，包括：
    - 订阅生命周期管理
    - 异步数据接收和处理
    - 数据缓存和回调机制
    - 多订阅并发支持
    """
    
    def __init__(self, max_data_per_subscription: int = 1000):
        """
        初始化订阅管理器
        
        Args:
            max_data_per_subscription: 每个订阅的最大数据缓存数量
        """
        self.max_data_per_subscription = max_data_per_subscription
        
        # 订阅管理
        self.subscriptions = {}  # {subscription_name: subscription_info}
        self.subscription_callbacks = {}  # {subscription_name: callback_function}
        self.subscription_data = defaultdict(lambda: deque(maxlen=max_data_per_subscription))
        self.subscription_threads = {}  # {subscription_name: thread}
        self.subscription_stop_events = {}  # {subscription_name: stop_event}
        
        # 线程安全
        self._lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'total_subscriptions': 0,
            'active_subscriptions': 0,
            'total_data_received': 0,
            'total_errors': 0
        }
        
        logger.info("订阅管理器已初始化")
    
    def create_subscription(self, subscription_name: str, config: Dict[str, Any], 
                          callback: Optional[Callable] = None) -> bool:
        """
        创建订阅
        
        Args:
            subscription_name: 订阅名称
            config: 订阅配置
            callback: 数据回调函数
            
        Returns:
            创建结果
        """
        with self._lock:
            if subscription_name in self.subscriptions:
                logger.warning(f"订阅 {subscription_name} 已存在")
                return False
            
            # 创建订阅信息
            subscription_info = {
                'name': subscription_name,
                'config': config,
                'status': SubscriptionStatus.STOPPED,
                'created_time': datetime.now(),
                'start_time': None,
                'data_count': 0,
                'error_count': 0,
                'last_data_time': None
            }
            
            self.subscriptions[subscription_name] = subscription_info
            
            if callback:
                self.subscription_callbacks[subscription_name] = callback
            
            self.stats['total_subscriptions'] += 1
            
            logger.info(f"订阅 {subscription_name} 已创建")
            return True
    
    def start_subscription(self, subscription_name: str, data_source: Callable) -> bool:
        """
        启动订阅
        
        Args:
            subscription_name: 订阅名称
            data_source: 数据源函数，返回原始数据
            
        Returns:
            启动结果
        """
        with self._lock:
            if subscription_name not in self.subscriptions:
                logger.error(f"订阅 {subscription_name} 不存在")
                return False
            
            subscription_info = self.subscriptions[subscription_name]
            
            if subscription_info['status'] != SubscriptionStatus.STOPPED:
                logger.warning(f"订阅 {subscription_name} 状态不是停止状态: {subscription_info['status']}")
                return False
            
            # 更新状态
            subscription_info['status'] = SubscriptionStatus.STARTING
            subscription_info['start_time'] = datetime.now()
            
            # 创建停止事件
            stop_event = threading.Event()
            self.subscription_stop_events[subscription_name] = stop_event
            
            # 启动工作线程
            thread = threading.Thread(
                target=self._subscription_worker,
                args=(subscription_name, data_source, stop_event),
                daemon=True,
                name=f"Subscription-{subscription_name}"
            )
            
            self.subscription_threads[subscription_name] = thread
            thread.start()
            
            self.stats['active_subscriptions'] += 1
            
            logger.info(f"订阅 {subscription_name} 已启动")
            return True
    
    def stop_subscription(self, subscription_name: str, timeout: float = 2.0) -> bool:
        """
        停止订阅
        
        Args:
            subscription_name: 订阅名称
            timeout: 等待超时时间（秒）
            
        Returns:
            停止结果
        """
        with self._lock:
            if subscription_name not in self.subscriptions:
                logger.warning(f"订阅 {subscription_name} 不存在")
                return False
            
            subscription_info = self.subscriptions[subscription_name]
            
            if subscription_info['status'] == SubscriptionStatus.STOPPED:
                logger.info(f"订阅 {subscription_name} 已经是停止状态")
                return True
            
            # 更新状态
            subscription_info['status'] = SubscriptionStatus.STOPPING
            
            # 设置停止事件
            if subscription_name in self.subscription_stop_events:
                self.subscription_stop_events[subscription_name].set()
            
            # 等待线程结束
            if subscription_name in self.subscription_threads:
                thread = self.subscription_threads[subscription_name]
                if thread.is_alive():
                    thread.join(timeout=timeout)
                    if thread.is_alive():
                        logger.warning(f"订阅线程 {subscription_name} 未能在 {timeout} 秒内结束")
            
            # 清理资源
            self._cleanup_subscription(subscription_name)
            
            # 更新状态
            subscription_info['status'] = SubscriptionStatus.STOPPED
            self.stats['active_subscriptions'] = max(0, self.stats['active_subscriptions'] - 1)
            
            logger.info(f"订阅 {subscription_name} 已停止")
            return True
    
    def remove_subscription(self, subscription_name: str) -> bool:
        """
        移除订阅
        
        Args:
            subscription_name: 订阅名称
            
        Returns:
            移除结果
        """
        with self._lock:
            if subscription_name not in self.subscriptions:
                logger.warning(f"订阅 {subscription_name} 不存在")
                return False
            
            # 先停止订阅
            self.stop_subscription(subscription_name)
            
            # 移除订阅信息
            del self.subscriptions[subscription_name]
            self.subscription_callbacks.pop(subscription_name, None)
            self.subscription_data.pop(subscription_name, None)
            
            logger.info(f"订阅 {subscription_name} 已移除")
            return True
    
    def get_subscription_data(self, subscription_name: str, count: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取订阅数据
        
        Args:
            subscription_name: 订阅名称
            count: 获取数据数量，None表示获取所有数据
            
        Returns:
            订阅数据列表
        """
        if subscription_name not in self.subscriptions:
            return []
        
        data_deque = self.subscription_data[subscription_name]
        
        if count is None:
            return list(data_deque)
        else:
            # 获取最新的count条数据
            return list(data_deque)[-count:] if count > 0 else []
    
    def clear_subscription_data(self, subscription_name: str) -> bool:
        """
        清空订阅数据
        
        Args:
            subscription_name: 订阅名称
            
        Returns:
            清空结果
        """
        if subscription_name not in self.subscriptions:
            return False
        
        self.subscription_data[subscription_name].clear()
        logger.info(f"订阅 {subscription_name} 数据已清空")
        return True
    
    def get_subscription_status(self, subscription_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取订阅状态
        
        Args:
            subscription_name: 订阅名称，None表示获取所有订阅状态
            
        Returns:
            订阅状态信息
        """
        with self._lock:
            if subscription_name:
                if subscription_name in self.subscriptions:
                    info = self.subscriptions[subscription_name].copy()
                    info['data_count'] = len(self.subscription_data[subscription_name])
                    info['has_callback'] = subscription_name in self.subscription_callbacks
                    return {subscription_name: info}
                else:
                    return {}
            else:
                # 返回所有订阅状态
                status = {}
                for name, info in self.subscriptions.items():
                    subscription_info = info.copy()
                    subscription_info['data_count'] = len(self.subscription_data[name])
                    subscription_info['has_callback'] = name in self.subscription_callbacks
                    status[name] = subscription_info
                return status
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息
        """
        with self._lock:
            stats = self.stats.copy()
            stats['active_subscriptions'] = len([
                s for s in self.subscriptions.values() 
                if s['status'] == SubscriptionStatus.RUNNING
            ])
            return stats

    def stop_all_subscriptions(self, timeout: float = 2.0) -> int:
        """
        停止所有订阅

        Args:
            timeout: 等待超时时间（秒）

        Returns:
            停止的订阅数量
        """
        with self._lock:
            subscription_names = list(self.subscriptions.keys())
            stopped_count = 0

            for name in subscription_names:
                if self.stop_subscription(name, timeout):
                    stopped_count += 1

            logger.info(f"已停止 {stopped_count} 个订阅")
            return stopped_count

    def _subscription_worker(self, subscription_name: str, data_source: Callable, stop_event: threading.Event):
        """
        订阅工作线程

        Args:
            subscription_name: 订阅名称
            data_source: 数据源函数
            stop_event: 停止事件
        """
        logger.info(f"订阅工作线程 {subscription_name} 已启动")

        try:
            # 更新订阅状态
            with self._lock:
                if subscription_name in self.subscriptions:
                    self.subscriptions[subscription_name]['status'] = SubscriptionStatus.RUNNING

            # 持续接收数据
            while not stop_event.is_set():
                try:
                    # 从数据源获取数据
                    raw_data = data_source()

                    if raw_data:
                        # 处理数据
                        processed_data = self._process_subscription_data(subscription_name, raw_data)

                        if processed_data:
                            # 存储数据
                            self.subscription_data[subscription_name].append(processed_data)

                            # 更新统计
                            with self._lock:
                                if subscription_name in self.subscriptions:
                                    self.subscriptions[subscription_name]['data_count'] += 1
                                    self.subscriptions[subscription_name]['last_data_time'] = datetime.now()

                                self.stats['total_data_received'] += 1

                            # 调用回调函数
                            if subscription_name in self.subscription_callbacks:
                                try:
                                    callback = self.subscription_callbacks[subscription_name]
                                    callback(processed_data)
                                except Exception as e:
                                    logger.error(f"订阅回调函数执行失败 {subscription_name}: {e}")
                                    with self._lock:
                                        self.stats['total_errors'] += 1

                            logger.debug(f"订阅 {subscription_name} 处理数据成功")

                    else:
                        # 没有数据时短暂休眠
                        time.sleep(0.01)

                except Exception as e:
                    if not stop_event.is_set():
                        logger.error(f"订阅 {subscription_name} 数据处理失败: {e}")
                        with self._lock:
                            if subscription_name in self.subscriptions:
                                self.subscriptions[subscription_name]['error_count'] += 1
                            self.stats['total_errors'] += 1

                        time.sleep(0.1)  # 错误后延迟重试

        except Exception as e:
            logger.error(f"订阅工作线程 {subscription_name} 异常: {e}")
            with self._lock:
                if subscription_name in self.subscriptions:
                    self.subscriptions[subscription_name]['status'] = SubscriptionStatus.ERROR

        finally:
            logger.info(f"订阅工作线程 {subscription_name} 已结束")

    def _process_subscription_data(self, subscription_name: str, raw_data: Any) -> Optional[Dict[str, Any]]:
        """
        处理订阅数据

        Args:
            subscription_name: 订阅名称
            raw_data: 原始数据

        Returns:
            处理后的数据，None表示处理失败
        """
        try:
            # 基础数据包装
            processed_data = {
                'subscription_name': subscription_name,
                'timestamp': datetime.now().isoformat(),
                'raw_data': raw_data,
                'data_size': len(raw_data) if hasattr(raw_data, '__len__') else 0
            }

            return processed_data

        except Exception as e:
            logger.error(f"处理订阅数据失败 {subscription_name}: {e}")
            return None

    def _cleanup_subscription(self, subscription_name: str):
        """
        清理订阅资源

        Args:
            subscription_name: 订阅名称
        """
        # 清理线程相关资源
        self.subscription_threads.pop(subscription_name, None)
        self.subscription_stop_events.pop(subscription_name, None)

        logger.debug(f"订阅 {subscription_name} 资源已清理")

    def __del__(self):
        """析构函数，确保所有订阅都被停止"""
        try:
            self.stop_all_subscriptions(timeout=1.0)
        except Exception as e:
            logger.error(f"订阅管理器析构时出错: {e}")
