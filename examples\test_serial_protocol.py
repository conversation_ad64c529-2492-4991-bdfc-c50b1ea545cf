#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
串口协议测试脚本

直接测试SerialProtocol类的数据接收能力

Author: Augment Agent
Date: 2025-08-02
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s.%(msecs)03d - %(name)s - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)

from src.hardware.protocols.serial_protocol import SerialProtocol

def test_serial_protocol():
    """测试串口协议"""
    
    # 串口配置
    config = {
        "port": "COM6",
        "baudrate": 115200,
        "bytesize": 8,
        "parity": "N",
        "stopbits": 1,
        "timeout": 1.0,
        "write_timeout": 1.0,
        "read_mode": "raw",
        "encoding": "utf-8",
        "eol": "\n",
        "xonxoff": False,
        "rtscts": False,
        "dsrdtr": False
    }
    
    print(f"串口配置: {config}")
    
    # 创建串口协议
    protocol = SerialProtocol("COM6")
    # 手动设置配置
    protocol._config.update(config)
    
    # 数据接收回调
    def on_data_received(data):
        print(f"✅ 接收到数据: {repr(data)} (类型: {type(data)})")
        if isinstance(data, bytes):
            print(f"   十六进制: {data.hex().upper()}")
    
    # 连接信号
    protocol._signals.data_received.connect(on_data_received)
    
    try:
        # 连接设备
        print("连接设备...")
        if not protocol.connect():
            print("❌ 设备连接失败")
            return
        
        print("✅ 设备连接成功")
        
        # 发送测试命令
        command = bytes.fromhex("49000B1205FF00041E010305C0000C4D")
        print(f"发送命令: {command.hex().upper()}")
        
        result = protocol.send_command(command, wait_response=True, timeout=5.0)
        print(f"发送结果: {result}")
        
        # 等待一段时间观察数据接收
        print("等待数据接收...")
        time.sleep(3)
        
    finally:
        # 断开连接
        protocol.disconnect()
        print("设备已断开连接")

if __name__ == "__main__":
    test_serial_protocol()
