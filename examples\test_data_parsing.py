#!/usr/bin/env python3
"""
IM948数据解析测试程序
测试数据解析配置是否正确
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.hardware.protocols.hex_template_engine import HexTemplateEngine
from src.hardware.protocols.protocol_config_parser import ProtocolConfigParser

def test_data_parsing():
    """测试数据解析功能"""
    
    # 模拟的IM948数据包
    test_data_hex = "49001311C00053AADF06CAFD6BFE1CBF000000000000D14D"
    test_data_bytes = bytes.fromhex(test_data_hex)
    
    print(f"测试数据: {test_data_hex}")
    print(f"数据长度: {len(test_data_bytes)} 字节")
    
    # 加载配置
    config_parser = ProtocolConfigParser()
    config = config_parser.load_config("examples/im948_config.json")
    
    # 获取get_euler_angles命令的响应配置
    command_config = config_parser.get_command_config(config, "get_euler_angles")
    response_config = command_config['response']
    
    print(f"响应配置: {response_config}")
    
    # 创建模板引擎
    template_engine = HexTemplateEngine()
    
    try:
        # 解析数据
        result = template_engine.parse_response(test_data_bytes, response_config)
        print(f"解析结果: {result}")
        
        # 显示解析的数据
        for field_name, field_data in result.items():
            if isinstance(field_data, dict) and 'value' in field_data:
                print(f"{field_name}: {field_data['value']} {field_data.get('unit', '')}")
            
    except Exception as e:
        print(f"解析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_data_parsing()
