#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IM948应答匹配机制测试脚本

测试新的应答验证功能，包括：
1. 配置文件解析测试
2. 应答匹配算法测试
3. 协议适配器集成测试
4. 错误处理机制测试

Author: Augment Agent
Date: 2025-08-02
"""

import sys
import os
import logging
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.hardware.protocols.protocol_config_parser import ProtocolConfigParser

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_response_validation.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)


class ResponseValidationTester:
    """应答验证测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.config_path = project_root / "examples" / "im948_config.json"
        self.parser = ProtocolConfigParser()
        self.test_results = []
        
        logger.info("应答验证测试器已初始化")
    
    def run_all_tests(self) -> bool:
        """
        运行所有测试
        
        Returns:
            所有测试是否通过
        """
        logger.info("开始运行应答验证测试套件")
        
        tests = [
            ("配置文件解析测试", self.test_config_parsing),
            ("期望应答解析测试", self.test_expected_response_parsing),
            ("应答匹配算法测试", self.test_response_matching),
            ("配置验证测试", self.test_config_validation),
            ("错误处理测试", self.test_error_handling)
        ]
        
        all_passed = True
        
        for test_name, test_func in tests:
            logger.info(f"运行测试: {test_name}")
            try:
                result = test_func()
                self.test_results.append((test_name, result, None))
                if result:
                    logger.info(f"✅ {test_name} - 通过")
                else:
                    logger.error(f"❌ {test_name} - 失败")
                    all_passed = False
            except Exception as e:
                logger.error(f"❌ {test_name} - 异常: {e}")
                self.test_results.append((test_name, False, str(e)))
                all_passed = False
        
        self.print_test_summary()
        return all_passed
    
    def test_config_parsing(self) -> bool:
        """测试配置文件解析"""
        try:
            # 加载配置文件
            config = self.parser.load_config(self.config_path)
            
            # 验证基本结构
            assert 'device_name' in config
            assert 'protocol' in config
            assert 'commands' in config['protocol']
            
            # 验证关键命令存在
            commands = config['protocol']['commands']
            assert 'set_subscription' in commands
            assert 'start_subscription' in commands
            
            logger.debug("配置文件解析测试通过")
            return True
            
        except Exception as e:
            logger.error(f"配置文件解析测试失败: {e}")
            return False
    
    def test_expected_response_parsing(self) -> bool:
        """测试期望应答解析"""
        try:
            config = self.parser.load_config(self.config_path)
            
            # 测试set_subscription命令的期望应答
            expected_response = self.parser.get_expected_response(config, 'set_subscription')
            assert expected_response == "49 00 01 12 13 4D"
            
            # 测试start_subscription命令的期望应答
            expected_response = self.parser.get_expected_response(config, 'start_subscription')
            assert expected_response == "49 00 01 19 1A 4D"
            
            # 测试has_expected_response方法
            assert self.parser.has_expected_response(config, 'set_subscription') == True
            assert self.parser.has_expected_response(config, 'start_subscription') == True
            
            # 测试不存在的命令
            assert self.parser.get_expected_response(config, 'nonexistent') is None
            assert self.parser.has_expected_response(config, 'nonexistent') == False
            
            logger.debug("期望应答解析测试通过")
            return True
            
        except Exception as e:
            logger.error(f"期望应答解析测试失败: {e}")
            return False
    
    def test_response_matching(self) -> bool:
        """测试应答匹配算法"""
        try:
            # 实现简化的匹配算法测试
            def match_response(received_data, expected_response):
                """简化的响应匹配算法"""
                try:
                    # 将期望响应转换为字节数据
                    expected_hex = expected_response.replace(' ', '').upper()
                    expected_bytes = bytes.fromhex(expected_hex)

                    # 处理接收到的数据
                    if isinstance(received_data, bytes):
                        received_bytes = received_data
                    elif isinstance(received_data, str):
                        # 如果是字符串，尝试解析为十六进制
                        try:
                            received_hex = received_data.replace(' ', '').upper()
                            received_bytes = bytes.fromhex(received_hex)
                        except ValueError:
                            # 如果不是十六进制字符串，编码为字节
                            received_bytes = received_data.encode('utf-8')
                    else:
                        # 其他类型，转换为字符串再编码
                        received_bytes = str(received_data).encode('utf-8')

                    # 进行字节级精确匹配
                    return received_bytes == expected_bytes

                except Exception as e:
                    logger.error(f"响应匹配过程中发生错误: {e}")
                    return False

            # 测试用例
            test_cases = [
                {
                    'received': bytes.fromhex("49000112134D"),
                    'expected': "49 00 01 12 13 4D",
                    'should_match': True,
                    'description': "完全匹配测试"
                },
                {
                    'received': bytes.fromhex("49000119134D"),
                    'expected': "49 00 01 12 13 4D",
                    'should_match': False,
                    'description': "不匹配测试"
                },
                {
                    'received': "49 00 01 12 13 4D",
                    'expected': "49 00 01 12 13 4D",
                    'should_match': True,
                    'description': "字符串格式匹配测试"
                },
                {
                    'received': bytes.fromhex("49000119134D"),
                    'expected': "49 00 01 19 1A 4D",
                    'should_match': False,
                    'description': "部分匹配测试（应该失败）"
                },
                {
                    'received': bytes.fromhex("490001191A4D"),
                    'expected': "49 00 01 19 1A 4D",
                    'should_match': True,
                    'description': "start_subscription命令匹配测试"
                }
            ]

            for i, test_case in enumerate(test_cases):
                result = match_response(
                    test_case['received'],
                    test_case['expected']
                )

                if result != test_case['should_match']:
                    logger.error(f"测试用例 {i+1} 失败: {test_case['description']}")
                    logger.error(f"期望匹配: {test_case['should_match']}, 实际结果: {result}")
                    return False

                logger.debug(f"测试用例 {i+1} 通过: {test_case['description']}")

            logger.debug("应答匹配算法测试通过")
            return True

        except Exception as e:
            logger.error(f"应答匹配算法测试失败: {e}")
            return False
    
    def test_config_validation(self) -> bool:
        """测试配置验证"""
        try:
            config = self.parser.load_config(self.config_path)
            
            # 测试响应配置验证
            result = self.parser.validate_response_config(config, 'set_subscription')
            assert result == True
            
            result = self.parser.validate_response_config(config, 'start_subscription')
            assert result == True
            
            # 测试成功条件获取
            success_condition = self.parser.get_response_success_condition(config, 'set_subscription')
            assert success_condition == "exact_match"
            
            logger.debug("配置验证测试通过")
            return True
            
        except Exception as e:
            logger.error(f"配置验证测试失败: {e}")
            return False
    
    def test_error_handling(self) -> bool:
        """测试错误处理"""
        try:
            config = self.parser.load_config(self.config_path)
            
            # 测试不存在命令的错误处理
            try:
                self.parser.validate_response_config(config, 'nonexistent_command')
                return False  # 应该抛出异常
            except ValueError as e:
                assert "不存在" in str(e)
                logger.debug("不存在命令错误处理正确")
            
            # 测试无效期望应答格式
            # 这里我们需要模拟一个无效的配置
            invalid_config = config.copy()
            invalid_config['protocol']['commands']['test_invalid'] = {
                'type': 'one_time',
                'response': {
                    'expected_response': 'INVALID_HEX_STRING'
                }
            }
            
            try:
                self.parser.validate_response_config(invalid_config, 'test_invalid')
                return False  # 应该抛出异常
            except ValueError as e:
                assert "格式无效" in str(e)
                logger.debug("无效期望应答格式错误处理正确")
            
            logger.debug("错误处理测试通过")
            return True
            
        except Exception as e:
            logger.error(f"错误处理测试失败: {e}")
            return False
    
    def print_test_summary(self):
        """打印测试摘要"""
        logger.info("\n" + "="*60)
        logger.info("应答验证测试摘要")
        logger.info("="*60)
        
        passed = 0
        failed = 0
        
        for test_name, result, error in self.test_results:
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"{test_name}: {status}")
            if error:
                logger.info(f"  错误: {error}")
            
            if result:
                passed += 1
            else:
                failed += 1
        
        logger.info("-" * 60)
        logger.info(f"总计: {len(self.test_results)} 个测试")
        logger.info(f"通过: {passed} 个")
        logger.info(f"失败: {failed} 个")
        logger.info(f"成功率: {passed/len(self.test_results)*100:.1f}%")
        logger.info("="*60)


def main():
    """主函数"""
    logger.info("开始IM948应答匹配机制测试")
    
    tester = ResponseValidationTester()
    success = tester.run_all_tests()
    
    if success:
        logger.info("🎉 所有测试通过！应答匹配机制工作正常。")
        return 0
    else:
        logger.error("💥 部分测试失败！请检查应答匹配机制。")
        return 1


if __name__ == "__main__":
    exit(main())
