#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
协议适配器测试脚本

专门测试协议适配器的应答验证机制

Author: Augment Agent
Date: 2025-08-02
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s.%(msecs)03d - %(name)s - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)

from src.hardware.protocols.hex_protocol_adapter import HexProtocolAdapter

def test_protocol_adapter():
    """测试协议适配器"""
    config_path = project_root / "examples" / "im948_config.json"
    
    print(f"配置文件路径: {config_path}")
    
    # 创建协议适配器
    adapter = HexProtocolAdapter(str(config_path))
    
    try:
        # 连接设备
        print("连接设备...")
        if not adapter.connect():
            print("❌ 设备连接失败")
            return
        
        print("✅ 设备连接成功")
        
        # 测试set_subscription命令
        print("\n测试set_subscription命令...")
        try:
            result = adapter.execute_command('set_subscription')
            print(f"命令执行结果: {result}")
            
            if result and result.get('success'):
                print("✅ set_subscription命令执行成功")
            else:
                print("❌ set_subscription命令执行失败")
                
        except Exception as e:
            print(f"❌ set_subscription命令异常: {e}")
        
        # 等待一下
        time.sleep(1)
        
        # 测试start_subscription命令
        print("\n测试start_subscription命令...")
        try:
            result = adapter.execute_command('start_subscription')
            print(f"命令执行结果: {result}")
            
            if result and result.get('success'):
                print("✅ start_subscription命令执行成功")
            else:
                print("❌ start_subscription命令执行失败")
                
        except Exception as e:
            print(f"❌ start_subscription命令异常: {e}")
        
    finally:
        # 断开连接
        adapter.disconnect()
        print("\n设备已断开连接")

if __name__ == "__main__":
    test_protocol_adapter()
