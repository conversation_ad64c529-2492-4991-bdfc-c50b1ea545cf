#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
十六进制协议框架单元测试

测试模板引擎、配置解析器、协议适配器和数据解析器的功能正确性。

Author: Augment Agent
Date: 2025-08-01
"""

import unittest
import tempfile
import json
import os
from pathlib import Path

try:
    from .hex_template_engine import HexTemplateEngine, ChecksumType, DataFormat
    from .protocol_config_parser import ProtocolConfigParser
    from .data_parser import DataParser, DataType, UnitConverter
except ImportError:
    # 如果相对导入失败，尝试直接导入
    from hex_template_engine import HexTemplateEngine, ChecksumType, DataFormat
    from protocol_config_parser import ProtocolConfigParser
    from data_parser import DataParser, DataType, UnitConverter


class TestHexTemplateEngine(unittest.TestCase):
    """测试十六进制模板引擎"""
    
    def setUp(self):
        """测试前准备"""
        self.engine = HexTemplateEngine()
    
    def test_simple_template_rendering(self):
        """测试简单模板渲染"""
        template = "#{header} #{device_id} #{command}"
        parameters = {
            "header": "AA BB",
            "device_id": "01",
            "command": "03"
        }
        
        result = self.engine.render_template(template, parameters)
        expected = bytes.fromhex("AABB0103")
        
        self.assertEqual(result, expected)
    
    def test_auto_checksum_sum8(self):
        """测试自动SUM8校验和计算"""
        template = "#{header} #{device_id} #{command} #{checksum}"
        parameters = {
            "header": "AA BB",
            "device_id": "01", 
            "command": "03",
            "checksum": "auto_sum8"
        }
        
        result = self.engine.render_template(template, parameters)
        
        # 验证校验和：AA + BB + 01 + 03 = 0x169, 0x169 & 0xFF = 0x69
        expected_checksum = (0xAA + 0xBB + 0x01 + 0x03) & 0xFF
        self.assertEqual(result[-1], expected_checksum)
    
    def test_auto_checksum_xor(self):
        """测试自动XOR校验和计算"""
        template = "#{data} #{checksum}"
        parameters = {
            "data": "01 02 03",
            "checksum": "auto_xor"
        }
        
        result = self.engine.render_template(template, parameters)
        
        # 验证校验和：01 ^ 02 ^ 03 = 0x00
        expected_checksum = 0x01 ^ 0x02 ^ 0x03
        self.assertEqual(result[-1], expected_checksum)
    
    def test_hex_string_conversion(self):
        """测试十六进制字符串转换"""
        test_cases = [
            ("AA BB CC", bytes([0xAA, 0xBB, 0xCC])),
            ("01-02-03", bytes([0x01, 0x02, 0x03])),
            ("FF:EE:DD", bytes([0xFF, 0xEE, 0xDD])),
            ("123", bytes([0x01, 0x23]))  # 奇数长度自动补0
        ]
        
        for hex_str, expected in test_cases:
            result = self.engine._hex_string_to_bytes(hex_str)
            self.assertEqual(result, expected)
    
    def test_data_parsing(self):
        """测试数据解析"""
        # 构造正确的响应数据，先计算校验和
        data_bytes = [0xAA, 0xBB, 0x01, 0x03, 0x04, 0x01, 0x90, 0x01, 0x95]
        checksum = sum(data_bytes) & 0xFF  # 计算校验和
        response = bytes(data_bytes + [checksum, 0xCC])

        config = {
            'fields': {
                'header': {'type': 'fixed', 'value': 'AA BB', 'position': 0},
                'device_id': {'type': 'match', 'position': 2},
                'function_code': {'type': 'match', 'position': 3},
                'data_length': {'type': 'length', 'position': 4},
                'data': {'type': 'data', 'position': 5, 'length_field': 'data_length'},
                'checksum': {'type': 'checksum', 'method': 'sum8', 'position': 9},  # 使用正索引
                'footer': {'type': 'fixed', 'value': 'CC', 'position': 10}  # 使用正索引
            },
            'data_parsing': {
                'temperature1': {
                    'offset': 0,
                    'length': 2,
                    'format': 'uint16_be',
                    'scale': 0.1,
                    'unit': '°C'
                },
                'temperature2': {
                    'offset': 2,
                    'length': 2,
                    'format': 'uint16_be',
                    'scale': 0.1,
                    'unit': '°C'
                }
            }
        }
        
        result = self.engine.parse_response(response, config)
        
        self.assertIn('temperature1', result)
        self.assertIn('temperature2', result)
        self.assertEqual(result['temperature1']['value'], 40.0)  # 0x0190 * 0.1
        self.assertEqual(result['temperature2']['value'], 40.5)  # 0x0195 * 0.1


class TestProtocolConfigParser(unittest.TestCase):
    """测试协议配置解析器"""
    
    def setUp(self):
        """测试前准备"""
        self.parser = ProtocolConfigParser()
        
        # 创建测试配置
        self.test_config = {
            "device_name": "测试温度传感器",
            "device_type": "temperature_sensor",
            "description": "测试用温度传感器",
            "connection": {
                "port": "COM2",
                "baudrate": 9600,
                "bytesize": 8,
                "parity": "N",
                "stopbits": 1,
                "timeout": 2.0
            },
            "protocol": {
                "type": "hex_template",
                "encoding": "hex",
                "commands": {
                    "read_temperature": {
                        "description": "读取温度",
                        "timeout": 3.0,
                        "request": {
                            "template": "#{header} #{device_id} #{command} #{checksum}",
                            "parameters": {
                                "header": "AA BB",
                                "device_id": "01",
                                "command": "03",
                                "checksum": "auto_sum8"
                            }
                        },
                        "response": {
                            "fields": {},
                            "data_parsing": {}
                        }
                    }
                }
            }
        }
    
    def test_config_validation_success(self):
        """测试配置验证成功"""
        result = self.parser.validate_config(self.test_config)
        self.assertTrue(result)
    
    def test_config_validation_missing_required(self):
        """测试缺少必需字段的配置验证"""
        invalid_config = self.test_config.copy()
        del invalid_config['device_name']
        
        with self.assertRaises(ValueError):
            self.parser.validate_config(invalid_config)
    
    def test_get_device_config(self):
        """测试获取设备配置"""
        device_config = self.parser.get_device_config(self.test_config)
        
        self.assertEqual(device_config['device_name'], '测试温度传感器')
        self.assertEqual(device_config['device_type'], 'temperature_sensor')
        self.assertEqual(device_config['connection']['port'], 'COM2')
    
    def test_get_command_config(self):
        """测试获取命令配置"""
        command_config = self.parser.get_command_config(self.test_config, 'read_temperature')
        
        self.assertIsNotNone(command_config)
        self.assertEqual(command_config['description'], '读取温度')
        self.assertEqual(command_config['timeout'], 3.0)
    
    def test_list_available_commands(self):
        """测试列出可用命令"""
        commands = self.parser.list_available_commands(self.test_config)
        
        self.assertIn('read_temperature', commands)
        self.assertEqual(len(commands), 1)
    
    def test_load_config_from_file(self):
        """测试从文件加载配置"""
        # 创建临时配置文件，指定UTF-8编码
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump(self.test_config, f, ensure_ascii=False, indent=2)
            temp_file = f.name
        
        try:
            # 加载配置
            loaded_config = self.parser.load_config(temp_file)
            self.assertEqual(loaded_config['device_name'], '测试温度传感器')
        finally:
            # 清理临时文件
            os.unlink(temp_file)


class TestDataParser(unittest.TestCase):
    """测试数据解析器"""
    
    def setUp(self):
        """测试前准备"""
        self.parser = DataParser()
    
    def test_unit_conversion(self):
        """测试单位转换"""
        # 温度转换
        result = UnitConverter.convert(25.0, '°C', '°F', 'temperature')
        self.assertAlmostEqual(result, 77.0, places=1)
        
        # 压力转换
        result = UnitConverter.convert(100.0, 'kPa', 'bar', 'pressure')
        self.assertAlmostEqual(result, 1.0, places=1)
    
    def test_sensor_data_parsing(self):
        """测试传感器数据解析"""
        raw_data = {
            'temperature': {
                'value': 250,
                'unit': '°C'
            }
        }
        
        parsing_config = {
            'temperature': {
                'data_type': 'temperature',
                'scale': 0.1,
                'target_unit': '°F',
                'precision': 1
            }
        }
        
        result = self.parser.parse_sensor_data(raw_data, parsing_config)
        
        self.assertIn('parsed_data', result)
        self.assertIn('temperature', result['parsed_data'])
        
        temp_data = result['parsed_data']['temperature']
        self.assertEqual(temp_data['raw_value'], 250)
        self.assertEqual(temp_data['processed_value'], 25.0)  # 250 * 0.1
        self.assertAlmostEqual(temp_data['final_value'], 77.0, places=1)  # 25°C -> 77°F
    
    def test_data_validation(self):
        """测试数据验证"""
        # 正常数据
        field_data = {'final_value': 25.0, 'data_type': 'temperature'}
        field_config = {'valid_range': (-50, 100)}
        
        result = self.parser._validate_field_data(field_data, field_config)
        self.assertTrue(result['valid'])
        
        # 超出范围的数据
        field_data = {'final_value': 150.0, 'data_type': 'temperature'}
        result = self.parser._validate_field_data(field_data, field_config)
        self.assertFalse(result['valid'])
        self.assertTrue(len(result['warnings']) > 0)


def run_comprehensive_test():
    """运行综合测试"""
    print("=== 十六进制协议框架综合测试 ===\n")
    
    # 创建测试实例
    engine = HexTemplateEngine()
    parser = ProtocolConfigParser()
    data_parser = DataParser()
    
    print("1. 测试模板引擎...")
    template = "#{header} #{device_id} #{command} #{data} #{checksum}"
    parameters = {
        "header": "AA BB",
        "device_id": "01",
        "command": "03",
        "data": "00 01",
        "checksum": "auto_sum8"
    }
    
    request = engine.render_template(template, parameters)
    print(f"   生成请求: {request.hex().upper()}")
    
    print("\n2. 测试配置解析...")
    # 创建示例配置文件
    config_path = "test_config.json"
    parser.create_sample_config(config_path)
    
    config = parser.load_config(config_path)
    print(f"   设备名称: {config['device_name']}")
    print(f"   可用命令: {parser.list_available_commands(config)}")
    
    print("\n3. 测试数据解析...")
    raw_data = {
        'temperature': {'value': 250, 'unit': '°C'}
    }
    parsing_config = {
        'temperature': {
            'data_type': 'temperature',
            'scale': 0.1,
            'target_unit': '°F',
            'precision': 1
        }
    }
    
    parsed_result = data_parser.parse_sensor_data(raw_data, parsing_config)
    temp_data = parsed_result['parsed_data']['temperature']
    print(f"   原始值: {temp_data['raw_value']} {temp_data['unit']}")
    print(f"   最终值: {temp_data['final_value']} {temp_data['converted_unit']}")
    
    # 清理测试文件
    if os.path.exists(config_path):
        os.unlink(config_path)
    
    print("\n✅ 综合测试完成！")


if __name__ == '__main__':
    # 运行单元测试
    print("运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "="*50)
    
    # 运行综合测试
    run_comprehensive_test()
