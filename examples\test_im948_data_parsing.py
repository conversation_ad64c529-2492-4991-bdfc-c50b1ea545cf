#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IM948数据解析测试脚本

使用模拟数据测试IM948协议的数据解析功能

Author: Augment Agent
Date: 2025-08-02
"""

import sys
import struct
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.hardware.protocols.data_parser import DataParser


def test_data_parsing():
    """测试数据解析"""
    print("=== IM948数据解析测试 ===")
    
    # 用户提供的典型应答报文
    test_response = "49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D"
    print(f"测试数据: {test_response}")
    
    # 转换为字节数组
    hex_bytes = test_response.replace(' ', '')
    data_bytes = bytes.fromhex(hex_bytes)
    print(f"字节长度: {len(data_bytes)}")
    
    # 解析协议结构
    print("\n=== 协议结构解析 ===")
    
    # 起始码和地址
    start_code = data_bytes[0]
    address = data_bytes[1]
    print(f"起始码: 0x{start_code:02X}")
    print(f"地址: 0x{address:02X}")
    
    # 数据体长度
    data_length = data_bytes[2]
    print(f"数据体长度: {data_length} 字节")
    
    # 功能标签
    function_tag = data_bytes[3]
    print(f"功能标签: 0x{function_tag:02X}")
    
    # 订阅掩码 (小端)
    subscription_mask = struct.unpack('<H', data_bytes[4:6])[0]
    print(f"订阅掩码: 0x{subscription_mask:04X} (bit6={bool(subscription_mask & 0x40)}, bit7={bool(subscription_mask & 0x80)})")
    
    # 时间戳 (小端)
    timestamp = struct.unpack('<I', data_bytes[6:10])[0]
    print(f"时间戳: {timestamp} (0x{timestamp:08X})")
    
    # 欧拉角数据 (3个S16小端)
    euler_data = struct.unpack('<hhh', data_bytes[10:16])
    roll_raw, pitch_raw, yaw_raw = euler_data
    
    # 应用缩放因子
    scale_factor = 0.0054931640625
    roll = roll_raw * scale_factor
    pitch = pitch_raw * scale_factor
    yaw = yaw_raw * scale_factor
    
    print(f"\n=== 欧拉角数据 ===")
    print(f"Roll原始值: {roll_raw} (0x{roll_raw & 0xFFFF:04X}) -> {roll:.6f}°")
    print(f"Pitch原始值: {pitch_raw} (0x{pitch_raw & 0xFFFF:04X}) -> {pitch:.6f}°")
    print(f"Yaw原始值: {yaw_raw} (0x{yaw_raw & 0xFFFF:04X}) -> {yaw:.6f}°")
    
    # 位置数据 (3个S16小端)
    position_data = struct.unpack('<hhh', data_bytes[16:22])
    x_raw, y_raw, z_raw = position_data
    
    # 应用缩放因子
    x = x_raw / 1000.0
    y = y_raw / 1000.0
    z = z_raw / 1000.0
    
    print(f"\n=== 位置数据 ===")
    print(f"X原始值: {x_raw} (0x{x_raw & 0xFFFF:04X}) -> {x:.3f}m")
    print(f"Y原始值: {y_raw} (0x{y_raw & 0xFFFF:04X}) -> {y:.3f}m")
    print(f"Z原始值: {z_raw} (0x{z_raw & 0xFFFF:04X}) -> {z:.3f}m")
    
    # 校验和
    checksum = data_bytes[22]
    end_code = data_bytes[23]
    print(f"\n=== 协议尾部 ===")
    print(f"校验和: 0x{checksum:02X}")
    print(f"结束码: 0x{end_code:02X}")
    
    # 验证校验和
    calculated_checksum = sum(data_bytes[1:22]) & 0xFF
    print(f"计算校验和: 0x{calculated_checksum:02X}")
    if calculated_checksum == checksum:
        print("✅ 校验和验证通过")
    else:
        print("❌ 校验和验证失败")


def test_data_parser_integration():
    """测试数据解析器集成"""
    print("\n=== 数据解析器集成测试 ===")
    
    # 初始化数据解析器
    parser = DataParser()
    
    # 模拟解析后的数据（模拟底层解析引擎的输出）
    parsed_data = {
        'subscription_mask': 0x00C0,
        'timestamp': 0x0605412F,
        'roll': -2106,  # 0xF7C6 as signed int16
        'pitch': 1288,  # 0x0508 as signed int16
        'yaw': -1,      # 0xFFFF as signed int16
        'x': -1096,     # 0xFBB8 as signed int16
        'y': 0,         # 0x0000 as signed int16
        'z': 0          # 0x0000 as signed int16
    }
    
    print("原始解析数据:")
    for key, value in parsed_data.items():
        print(f"  {key}: {value}")
    
    # 应用缩放因子
    scale_factor = 0.0054931640625
    scaled_data = {
        'subscription_mask': parsed_data['subscription_mask'],
        'timestamp': parsed_data['timestamp'],
        'roll': parsed_data['roll'] * scale_factor,
        'pitch': parsed_data['pitch'] * scale_factor,
        'yaw': parsed_data['yaw'] * scale_factor,
        'x': parsed_data['x'] / 1000.0,
        'y': parsed_data['y'] / 1000.0,
        'z': parsed_data['z'] / 1000.0
    }
    
    print("\n应用缩放因子后:")
    print(f"  欧拉角: Roll={scaled_data['roll']:.6f}°, Pitch={scaled_data['pitch']:.6f}°, Yaw={scaled_data['yaw']:.6f}°")
    print(f"  位置: X={scaled_data['x']:.3f}m, Y={scaled_data['y']:.3f}m, Z={scaled_data['z']:.3f}m")
    print(f"  时间戳: {scaled_data['timestamp']}")
    print(f"  订阅掩码: 0x{scaled_data['subscription_mask']:04X}")
    
    # 验证数据范围
    print("\n=== 数据范围验证 ===")
    
    # 欧拉角范围检查
    if -180.0 <= scaled_data['roll'] <= 180.0:
        print("✅ Roll角度范围正常")
    else:
        print(f"❌ Roll角度超出范围: {scaled_data['roll']:.6f}°")
    
    if -90.0 <= scaled_data['pitch'] <= 90.0:
        print("✅ Pitch角度范围正常")
    else:
        print(f"❌ Pitch角度超出范围: {scaled_data['pitch']:.6f}°")
    
    if -180.0 <= scaled_data['yaw'] <= 180.0:
        print("✅ Yaw角度范围正常")
    else:
        print(f"❌ Yaw角度超出范围: {scaled_data['yaw']:.6f}°")
    
    # 位置范围检查（假设±1000m是合理范围）
    for axis in ['x', 'y', 'z']:
        if -1000.0 <= scaled_data[axis] <= 1000.0:
            print(f"✅ {axis.upper()}轴位置范围正常")
        else:
            print(f"❌ {axis.upper()}轴位置超出范围: {scaled_data[axis]:.3f}m")


if __name__ == "__main__":
    test_data_parsing()
    test_data_parser_integration()
    print("\n=== 测试完成 ===")
