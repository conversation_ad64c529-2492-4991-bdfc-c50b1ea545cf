# 4. 项目规划与任务管理文档

| 版本 | 最后更新时间 | 更新者 | 变更摘要 |
| :--- | :--- | :--- | :--- |
| 1.0  | 2025-07-31 14:15:00 +08:00 | PL | 初始创建详细开发计划 |
| 2.0  | 2025-07-31 21:30:00 +08:00 | PL | 基于解决方案架构更新，聚焦剩余开发任务 |

---

## 0. 项目概述

### 0.1 项目目标 🎯 **已更新**
**基于渐进式完善策略，完成数据可视化系统的功能增强**，重点实现串口协议增强和数据处理层构建。

### 0.2 项目现状 ✅ **已完成部分**
- **第一阶段：图表功能完善** ✅ **已完成**
  - 散点图功能增强 ✅
  - 柱状图功能增强 ✅
  - 热力图功能增强 ✅
  - 雷达图功能增强 ✅（包括Matplotlib重构、中文支持、文本重叠解决）

### 0.3 剩余开发目标
- **第二阶段：串口协议增强** 🔄 **当前重点**
  - RS232协议支持
  - RS485协议支持
  - 自由口协议支持
- **第三阶段：数据处理层构建** 🔄 **后续开发**
  - 基础统计分析
  - 基础滤波处理
  - 基础异常检测

### 0.4 技术基础 ✅ **已就绪**
- **现有架构：** 基于PyQt5 + pyqtgraph的成熟技术栈
- **扩展策略：** 在现有SerialProtocol基础上扩展，保持85%代码复用率
- **开发环境：** Python 3.12.4 + 完整的依赖环境

## 1. 项目任务分解结构 (WBS)

### 1.1 总体项目结构

```
数据可视化系统功能增强项目 [DVS-Enhancement-2025]
├── ✅ 阶段1：图表功能完善 [CHART-ENHANCEMENT] - 已完成
│   ├── ✅ 散点图功能增强 [SCATTER-ENHANCE] - 已完成
│   ├── ✅ 柱状图功能增强 [BAR-ENHANCE] - 已完成
│   ├── ✅ 热力图功能增强 [HEATMAP-ENHANCE] - 已完成
│   └── ✅ 雷达图功能增强 [RADAR-ENHANCE] - 已完成
├── 🔄 阶段2：串口协议增强 [SERIAL-PROTOCOL-ENHANCE] - 当前开发
│   ├── RS232协议支持实现 [RS232-PROTOCOL-IMPL]
│   ├── RS485协议支持实现 [RS485-PROTOCOL-IMPL]
│   ├── 自由口协议支持实现 [FREEPORT-PROTOCOL-IMPL]
│   └── 串口协议集成测试 [SERIAL-INTEGRATION-TEST]
└── 🔄 阶段3：数据处理层构建 [DATA-PROCESSING-LAYER] - 后续开发
    ├── 基础统计分析模块 [BASIC-STATS-MODULE]
    ├── 基础滤波处理模块 [BASIC-FILTER-MODULE]
    ├── 基础异常检测模块 [BASIC-ANOMALY-MODULE]
    └── 数据处理集成测试 [DATA-PROCESSING-TEST]
```

## 2. 详细任务规划

### 2.1 ✅ 阶段1：图表功能完善 - 已完成

**完成状态：** ✅ **全部完成**
- ✅ 散点图功能增强：支持颜色映射、大小映射、分组显示、交互功能
- ✅ 柱状图功能增强：支持分组、堆叠、水平显示、动态更新
- ✅ 热力图功能增强：支持多种颜色映射、数值标注、交互缩放
- ✅ 雷达图功能增强：完整的Matplotlib重构，解决中文显示和文本重叠问题

**技术成果：**
- 完整的Matplotlib雷达图实现，支持中文字体和智能文本渲染
- 所有图表类型功能丰富，支持多样化数据可视化
- 保持与现有BaseChart架构的完全兼容性

### 2.2 🔄 阶段2：串口协议增强 (预计1周，当前开发重点)

#### 任务2.1：RS232协议支持实现 [RS232-PROTOCOL-IMPL]
**工作量：** 2天
**优先级：** 最高
**依赖关系：** 基于现有SerialProtocol基类

**详细工作内容：**
- 扩展现有SerialProtocol类，实现RS232特定功能
- 实现硬件流控制（RTS/CTS, DTR/DSR）
- 配置RS232特定的通信参数
- 实现连接状态监控和错误处理
- 创建RS232协议测试用例

**技术要点：**
```python
class RS232Protocol(SerialProtocol):
    def __init__(self, **kwargs):
        # RS232特定配置
        rs232_config = {
            "rtscts": True,      # 硬件流控
            "dsrdtr": True,      # DTR/DSR控制
            "xonxoff": False,    # 禁用软件流控
            "timeout": 1.0,      # 读取超时
        }
        super().__init__(**{**kwargs, **rs232_config})

    def configure_handshake(self):
        """配置RS232握手信号"""
        pass

    def monitor_control_lines(self):
        """监控控制线状态"""
        pass
```

**验收标准：**
- [ ] RS232协议类实现完整
- [ ] 硬件流控制功能正常
- [ ] 连接状态监控准确
- [ ] 错误处理机制完善
- [ ] 单元测试通过

#### 任务2.2：RS485协议支持实现 [RS485-PROTOCOL-IMPL]
**工作量：** 2天
**优先级：** 高
**依赖关系：** 依赖任务2.1完成

**详细工作内容：**
- 实现RS485半双工通信机制
- 实现自动方向控制（发送/接收切换）
- 配置RS485特定的通信参数
- 实现多点通信支持
- 创建RS485协议测试用例

**技术要点：**
```python
class RS485Protocol(SerialProtocol):
    def __init__(self, **kwargs):
        # RS485特定配置
        rs485_config = {
            "rtscts": False,     # 禁用硬件流控
            "half_duplex": True, # 半双工模式
            "auto_direction": True, # 自动方向控制
            "address_mode": True,   # 支持地址模式
        }
        super().__init__(**{**kwargs, **rs485_config})

    def set_transmit_mode(self):
        """设置为发送模式"""
        pass

    def set_receive_mode(self):
        """设置为接收模式"""
        pass

    def send_with_address(self, address, data):
        """带地址的数据发送"""
        pass
```

**验收标准：**
- [ ] RS485协议类实现完整
- [ ] 半双工通信功能正常
- [ ] 自动方向控制准确
- [ ] 多点通信支持完善
- [ ] 单元测试通过

#### 任务2.3：自由口协议支持实现 [FREEPORT-PROTOCOL-IMPL]
**工作量：** 2天
**优先级：** 中
**依赖关系：** 依赖任务2.2完成

**详细工作内容：**
- 实现灵活的数据解析框架
- 支持用户自定义数据格式
- 实现多种帧分隔符支持
- 创建数据解析规则配置系统
- 创建自由口协议测试用例

**技术要点：**
```python
class FreePortProtocol(SerialProtocol):
    def __init__(self, **kwargs):
        # 自由口特定配置
        self.data_parser = kwargs.get('parser', self.default_parser)
        self.frame_delimiter = kwargs.get('delimiter', '\n')
        self.custom_format = kwargs.get('format', 'auto')
        super().__init__(**kwargs)

    def parse_data(self, raw_data):
        """支持用户自定义数据解析规则"""
        return self.data_parser(raw_data)

    def default_parser(self, data):
        """默认数据解析器"""
        pass

    def set_custom_parser(self, parser_func):
        """设置自定义解析器"""
        self.data_parser = parser_func

    def configure_frame_format(self, delimiter, header=None, footer=None):
        """配置帧格式"""
        pass
```

**验收标准：**
- [ ] 自由口协议类实现完整
- [ ] 自定义解析器功能正常
- [ ] 多种帧格式支持完善
- [ ] 配置系统易用性良好
- [ ] 单元测试通过

#### 任务2.4：串口协议集成测试 [SERIAL-INTEGRATION-TEST]
**工作量：** 1天
**优先级：** 高
**依赖关系：** 依赖任务2.1, 2.2, 2.3完成

**详细工作内容：**
- 创建串口协议集成测试套件
- 测试三种协议的兼容性和稳定性
- 实现协议切换和配置管理
- 创建性能基准测试
- 编写用户使用文档

**技术要点：**
```python
class SerialProtocolManager:
    def __init__(self):
        self.protocols = {
            'RS232': RS232Protocol,
            'RS485': RS485Protocol,
            'FreePort': FreePortProtocol
        }
        self.current_protocol = None

    def create_protocol(self, protocol_type, **config):
        """创建指定类型的协议实例"""
        if protocol_type in self.protocols:
            return self.protocols[protocol_type](**config)
        raise ValueError(f"Unsupported protocol: {protocol_type}")

    def switch_protocol(self, new_protocol_type, **config):
        """切换协议类型"""
        if self.current_protocol:
            self.current_protocol.close()
        self.current_protocol = self.create_protocol(new_protocol_type, **config)
        return self.current_protocol
```

**验收标准：**
- [ ] 集成测试套件完整
- [ ] 三种协议功能正常
- [ ] 协议切换机制稳定
- [ ] 性能指标达标
- [ ] 文档完整清晰

### 2.3 🔄 阶段3：数据处理层构建 (预计1周，后续开发)

#### 任务3.1：基础统计分析模块 [BASIC-STATS-MODULE]
**工作量：** 2天
**优先级：** 中
**依赖关系：** 基于现有EventBus和ThreadManager

**详细工作内容：**
- 创建DataAnalyzer类，集成现有事件系统
- 实现基础统计指标计算（均值、方差、标准差等）
- 实现统计结果缓存机制
- 创建统计数据可视化接口
- 集成到现有图表系统

**技术要点：**
```python
class DataAnalyzer:
    def __init__(self, event_bus):
        self.event_bus = event_bus
        self.stats_cache = {}
        self.event_bus.subscribe('data_updated', self.on_data_updated)

    def calculate_basic_stats(self, data):
        """基础统计：均值、方差、标准差、最大值、最小值"""
        return {
            'mean': np.mean(data),
            'std': np.std(data),
            'var': np.var(data),
            'min': np.min(data),
            'max': np.max(data),
            'count': len(data),
            'median': np.median(data)
        }

    def calculate_advanced_stats(self, data):
        """高级统计：偏度、峰度、分位数"""
        from scipy import stats
        return {
            'skewness': stats.skew(data),
            'kurtosis': stats.kurtosis(data),
            'q25': np.percentile(data, 25),
            'q75': np.percentile(data, 75)
        }
```

**验收标准：**
- [ ] 统计分析模块实现完整
- [ ] 事件系统集成正常
- [ ] 缓存机制工作稳定
- [ ] 可视化接口友好
- [ ] 性能满足1KHz需求

#### 任务3.2：基础滤波处理模块 [BASIC-FILTER-MODULE]
**工作量：** 2天
**优先级：** 中
**依赖关系：** 依赖任务3.1完成

**详细工作内容：**
- 创建DataFilter类，实现常用数字滤波器
- 实现低通、高通、带通、中值滤波
- 支持实时滤波处理
- 创建滤波参数配置界面
- 集成到数据处理流水线

**技术要点：**
```python
class DataFilter:
    def __init__(self):
        self.filter_cache = {}
        self.filter_history = {}

    def apply_filter(self, data, filter_type='lowpass', **params):
        """应用指定类型的滤波器"""
        if filter_type == 'lowpass':
            return self.lowpass_filter(data, **params)
        elif filter_type == 'highpass':
            return self.highpass_filter(data, **params)
        elif filter_type == 'median':
            return self.median_filter(data, **params)
        elif filter_type == 'bandpass':
            return self.bandpass_filter(data, **params)

    def lowpass_filter(self, data, cutoff_freq, sample_rate):
        """低通滤波器"""
        from scipy.signal import butter, filtfilt
        nyquist = sample_rate * 0.5
        normal_cutoff = cutoff_freq / nyquist
        b, a = butter(6, normal_cutoff, btype='low', analog=False)
        return filtfilt(b, a, data)

    def median_filter(self, data, window_size=5):
        """中值滤波器"""
        from scipy.signal import medfilt
        return medfilt(data, kernel_size=window_size)
```

**验收标准：**
- [ ] 滤波模块实现完整
- [ ] 多种滤波器功能正常
- [ ] 实时处理性能达标
- [ ] 参数配置界面友好
- [ ] 流水线集成稳定

#### 任务3.3：基础异常检测模块 [BASIC-ANOMALY-MODULE]
**工作量：** 2天
**优先级：** 中
**依赖关系：** 依赖任务3.2完成

**详细工作内容：**
- 创建AnomalyDetector类，实现异常检测算法
- 实现3σ准则、IQR方法、移动平均等检测方法
- 支持实时异常检测和报警
- 创建异常检测结果可视化
- 集成到监控系统

**技术要点：**
```python
class AnomalyDetector:
    def __init__(self, event_bus):
        self.event_bus = event_bus
        self.detection_methods = {
            '3sigma': self.three_sigma_detection,
            'iqr': self.iqr_detection,
            'moving_avg': self.moving_average_detection
        }

    def detect_outliers(self, data, method='3sigma', **params):
        """检测异常值"""
        if method in self.detection_methods:
            return self.detection_methods[method](data, **params)
        raise ValueError(f"Unknown detection method: {method}")

    def three_sigma_detection(self, data, threshold=3):
        """3σ准则异常检测"""
        mean = np.mean(data)
        std = np.std(data)
        threshold_value = threshold * std
        outliers = np.abs(data - mean) > threshold_value
        return outliers, {'mean': mean, 'std': std, 'threshold': threshold_value}

    def iqr_detection(self, data, factor=1.5):
        """IQR方法异常检测"""
        q1 = np.percentile(data, 25)
        q3 = np.percentile(data, 75)
        iqr = q3 - q1
        lower_bound = q1 - factor * iqr
        upper_bound = q3 + factor * iqr
        outliers = (data < lower_bound) | (data > upper_bound)
        return outliers, {'q1': q1, 'q3': q3, 'iqr': iqr, 'bounds': (lower_bound, upper_bound)}
```

**验收标准：**
- [ ] 异常检测模块实现完整
- [ ] 多种检测方法功能正常
- [ ] 实时检测性能达标
- [ ] 可视化效果良好
- [ ] 报警机制稳定

#### 任务3.4：数据处理集成测试 [DATA-PROCESSING-TEST]
**工作量：** 1天
**优先级：** 高
**依赖关系：** 依赖任务3.1, 3.2, 3.3完成

**详细工作内容：**
- 创建数据处理模块集成测试套件
- 测试统计分析、滤波、异常检测的协同工作
- 验证1KHz数据处理性能指标
- 创建数据处理流水线配置管理
- 编写数据处理模块使用文档

**技术要点：**
```python
class DataProcessingPipeline:
    def __init__(self, event_bus):
        self.event_bus = event_bus
        self.analyzer = DataAnalyzer(event_bus)
        self.filter = DataFilter()
        self.anomaly_detector = AnomalyDetector(event_bus)
        self.pipeline_config = {}

    def process_data(self, data, config=None):
        """数据处理流水线"""
        if config is None:
            config = self.pipeline_config

        # 1. 数据滤波
        if config.get('enable_filter', False):
            data = self.filter.apply_filter(data, **config.get('filter_params', {}))

        # 2. 统计分析
        stats = self.analyzer.calculate_basic_stats(data)

        # 3. 异常检测
        outliers, anomaly_info = self.anomaly_detector.detect_outliers(
            data, **config.get('anomaly_params', {})
        )

        return {
            'processed_data': data,
            'statistics': stats,
            'outliers': outliers,
            'anomaly_info': anomaly_info
        }
```

**验收标准：**
- [ ] 集成测试套件完整
- [ ] 模块协同工作正常
- [ ] 性能指标达标（1KHz）
- [ ] 配置管理系统稳定
- [ ] 文档完整清晰

## 3. 项目时间计划

### 3.1 总体时间安排 🎯 **已更新**
- **项目总工期：** 2周（基于解决方案架构文档的时间安排）
- **✅ 阶段1：图表功能完善** - 已完成（1.5周）
- **🔄 阶段2：串口协议增强** - 当前开发（1周）
- **🔄 阶段3：数据处理层构建** - 后续开发（1周）

### 3.2 关键里程碑 🎯 **已更新**
| 里程碑 | 时间点 | 交付物 | 验收标准 |
|--------|--------|--------|----------|
| ✅ M1: 图表功能完善完成 | 已完成 | 4种图表功能增强 | 功能丰富，用户体验良好 |
| 🔄 M2: 串口协议增强完成 | 1周后 | 3种串口协议支持 | RS232/RS485/自由口协议完整支持 |
| 🔄 M3: 数据处理层完成 | 2周后 | 完整数据处理系统 | 统计/滤波/异常检测功能完整 |

### 3.3 关键路径分析 🎯 **已更新**
**当前关键路径：** 任务2.1 → 任务2.2 → 任务2.3 → 任务2.4 → 任务3.1 → 任务3.2 → 任务3.3 → 任务3.4

**关键路径总时长：** 2周

### 3.4 并行开发机会
- 任务2.1, 2.2, 2.3可以部分并行开发（不同协议独立实现）
- 任务3.1, 3.2, 3.3可以部分并行开发（不同处理模块独立实现）

## 4. 风险管理 🎯 **已更新**

### 4.1 技术风险
| 风险 | 概率 | 影响 | 应对策略 |
|------|------|------|----------|
| 串口协议兼容性问题 | 中 | 高 | 基于现有SerialProtocol扩展，降低风险 |
| 数据处理性能不达标 | 中 | 中 | 实现缓存和优化机制，分阶段性能测试 |
| 第三方库依赖问题 | 低 | 中 | 使用成熟稳定的scipy、numpy库 |
| 现有系统集成困难 | 低 | 高 | 保持现有接口不变，渐进式集成 |

### 4.2 进度风险
| 风险 | 概率 | 影响 | 应对策略 |
|------|------|------|----------|
| 任务工作量估算偏差 | 中 | 中 | 预留20%缓冲时间，分阶段评估 |
| 串口协议测试困难 | 中 | 高 | 准备多种测试设备，模拟测试环境 |
| 数据处理算法复杂度超预期 | 中 | 中 | 优先实现基础功能，高级功能可选 |
| 用户需求变更 | 低 | 高 | 保持接口灵活性，模块化设计 |

## 5. 质量保证 🎯 **已更新**

### 5.1 代码质量标准
- **代码覆盖率：** ≥ 80%
- **代码复杂度：** 圈复杂度 ≤ 10
- **代码规范：** 遵循PEP 8标准
- **文档完整性：** 所有公共接口有文档
- **接口兼容性：** 与现有系统完全兼容

### 5.2 测试策略
- **单元测试：** 覆盖所有核心功能模块
- **集成测试：** 验证模块间协作和系统集成
- **性能测试：** 验证1KHz数据处理性能指标
- **兼容性测试：** 验证与现有系统的兼容性
- **用户验收测试：** 使用真实数据和场景验证

### 5.3 质量门禁
- **阶段2门禁：** 三种串口协议功能完整，集成测试通过
- **阶段3门禁：** 数据处理性能达标，用户验收测试通过
- **最终门禁：** 所有功能完整，文档齐全，质量标准达标

## 5. 质量保证

### 5.1 代码质量标准
- **代码覆盖率：** ≥ 80%
- **代码复杂度：** 圈复杂度 ≤ 10
- **代码规范：** 遵循PEP 8标准
- **文档完整性：** 所有公共接口有文档

### 5.2 测试策略
- **单元测试：** 覆盖所有核心功能
- **集成测试：** 验证组件间协作
- **性能测试：** 验证渲染性能指标
- **用户验收测试：** 使用真实数据验证

### 5.3 质量门禁
- **阶段1门禁：** 基础雷达图渲染成功
- **阶段2门禁：** 文本重叠问题解决
- **阶段3门禁：** 用户验收测试通过

## 6. 资源配置 🎯 **已更新**

### 6.1 人力资源
- **开发人员：** 1名（LD角色）
- **测试人员：** 1名（兼任）
- **项目管理：** 1名（PL角色）

### 6.2 技术资源
- **开发环境：** Python 3.12.4 + PyQt5 + 相关依赖库
- **测试设备：** 串口测试设备（RS232/RS485）
- **开发工具：** IDE、版本控制、测试框架

### 6.3 时间资源
- **总工期：** 2周
- **每日工作时间：** 8小时
- **缓冲时间：** 20%（预留风险应对）

## 7. 沟通计划 🎯 **已更新**

### 7.1 进度报告
- **频率：** 每个阶段完成后
- **内容：** 完成情况、遇到问题、下阶段计划
- **方式：** 更新任务状态和文档

### 7.2 问题升级
- **技术问题：** 立即报告并寻求支持
- **进度延误：** 及时调整计划和资源
- **质量问题：** 暂停进度，优先解决

## 8. 成功标准 🎯 **已更新**

### 8.1 阶段2成功标准（串口协议增强）
- **RS232协议支持：** 硬件流控制功能正常，连接稳定
- **RS485协议支持：** 半双工通信正常，多点通信稳定
- **自由口协议支持：** 自定义解析器功能完整，配置灵活
- **协议集成：** 三种协议切换正常，性能达标

### 8.2 阶段3成功标准（数据处理层构建）
- **统计分析模块：** 基础统计指标计算准确，性能达标
- **滤波处理模块：** 多种滤波器功能正常，实时处理稳定
- **异常检测模块：** 检测算法准确，报警机制可靠
- **集成测试：** 模块协同工作正常，1KHz性能达标

### 8.3 技术成功标准
- **架构设计合理：** 模块化，可扩展，易维护
- **代码质量高：** 遵循最佳实践，注释完整
- **测试覆盖完整：** 单元测试和集成测试全覆盖
- **文档齐全：** 技术文档和用户文档完整

### 8.4 用户体验成功标准
- **操作简单：** 接口保持不变，学习成本为零
- **功能丰富：** 串口协议支持完整，数据处理功能强大
- **响应速度快：** 实时处理，无明显延迟
- **稳定性好：** 长时间运行无问题

---

## 9. 总结 🎯 **已更新**

本项目规划基于解决方案架构文档的**渐进式完善策略**，在已完成图表功能完善的基础上，聚焦于串口协议增强和数据处理层构建。

**当前状态：**
- ✅ **阶段1已完成：** 图表功能完善，包括雷达图Matplotlib重构
- 🔄 **阶段2进行中：** 串口协议增强，支持RS232/RS485/自由口协议
- 🔄 **阶段3待开发：** 数据处理层构建，实现统计/滤波/异常检测

**核心优势：**
- **技术基础扎实：** 基于现有成熟架构扩展
- **问题导向：** 直接解决用户实际需求
- **兼容性好：** 保持现有接口不变
- **可扩展性强：** 为未来功能扩展奠定基础

**预期成果：**
- 完整的串口协议支持体系
- 强大的数据处理分析能力
- 提升系统整体功能完整性
- 建立可复用的功能扩展模式

**下一步行动：**
1. 立即开始阶段2串口协议增强开发
2. 严格按照时间计划执行各阶段任务
3. 持续跟踪进度和质量指标
4. 及时处理风险和问题

---

**文档维护者：** PL (Planning Expert)
**最后更新：** 2025-07-31 21:30:00 +08:00
**状态：** 已更新，聚焦剩余开发任务