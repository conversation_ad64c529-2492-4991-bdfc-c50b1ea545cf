#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
十六进制协议适配器模块

实现工业自动化十六进制协议的完整适配功能。
集成模板引擎、配置解析器和串口协议，提供统一的设备通信接口。

Author: Augment Agent
Date: 2025-08-01
"""

import logging
import time
import threading
from typing import Any, Dict, List, Optional, Union, Callable
from pathlib import Path
from datetime import datetime
from collections import defaultdict

from .hex_template_engine import HexTemplateEngine
from .protocol_config_parser import ProtocolConfigParser
from .serial_protocol import SerialProtocol
from ..protocol_base import ProtocolEventType, ConnectionError

# 获取logger
logger = logging.getLogger(__name__)


class HexProtocolAdapter:
    """
    十六进制协议适配器
    
    提供完整的十六进制协议设备通信功能。
    集成模板引擎、配置解析和串口通信，支持多种工业设备协议。
    """
    
    def __init__(self, config_path: Union[str, Path]):
        """
        初始化协议适配器
        
        Args:
            config_path: 协议配置文件路径
            
        Raises:
            FileNotFoundError: 配置文件不存在
            ValueError: 配置文件格式错误
        """
        self.config_path = Path(config_path)
        
        # 初始化组件
        self.config_parser = ProtocolConfigParser()
        self.template_engine = HexTemplateEngine()
        self.serial_protocol = SerialProtocol()
        
        # 加载配置
        self.config = self.config_parser.load_config(self.config_path)
        self.device_config = self.config_parser.get_device_config(self.config)
        self.protocol_config = self.config_parser.get_protocol_config(self.config)
        
        # 配置串口
        self._configure_serial()
        
        # 统计信息
        self.stats = {
            'commands_sent': 0,
            'responses_received': 0,
            'errors': 0,
            'last_communication': None
        }

        # 订阅管理相关属性
        self.subscriptions = {}  # 活跃订阅: {subscription_name: subscription_info}
        self.subscription_callbacks = {}  # 订阅回调: {subscription_name: callback_function}
        self.subscription_data = defaultdict(list)  # 订阅数据缓存: {subscription_name: [data_list]}
        self.subscription_threads = {}  # 订阅线程: {subscription_name: thread}
        self.subscription_stop_events = {}  # 停止事件: {subscription_name: stop_event}
        self._subscription_lock = threading.Lock()  # 订阅操作锁

        logger.info(f"十六进制协议适配器已初始化: {self.device_config['device_name']}")
    
    def connect(self) -> bool:
        """
        连接设备
        
        Returns:
            连接结果
        """
        try:
            logger.info(f"连接设备: {self.device_config['device_name']}")
            
            if self.serial_protocol.connect():
                logger.info("设备连接成功")
                return True
            else:
                logger.error("设备连接失败")
                return False
                
        except Exception as e:
            logger.error(f"连接设备异常: {e}")
            self.stats['errors'] += 1
            return False
    
    def disconnect(self) -> bool:
        """
        断开设备连接

        Returns:
            断开结果
        """
        try:
            logger.info("断开设备连接")

            # 停止所有订阅
            if self.subscriptions:
                logger.info("停止所有活跃订阅")
                self.stop_all_subscriptions()

            if self.serial_protocol.disconnect():
                logger.info("设备断开成功")
                return True
            else:
                logger.error("设备断开失败")
                return False

        except Exception as e:
            logger.error(f"断开设备异常: {e}")
            return False
    
    def is_connected(self) -> bool:
        """
        检查连接状态

        Returns:
            连接状态
        """
        return self.serial_protocol._is_connected
    
    def execute_command(self, command_name: str, **extra_params) -> Dict[str, Any]:
        """
        执行协议命令
        
        Args:
            command_name: 命令名称
            **extra_params: 额外参数，会覆盖配置中的默认参数
            
        Returns:
            解析后的响应数据
            
        Raises:
            ValueError: 命令不存在或参数错误
            ConnectionError: 设备未连接或通信失败
        """
        try:
            logger.info(f"执行命令: {command_name}")
            
            # 检查连接状态
            if not self.is_connected():
                raise ConnectionError("设备未连接")
            
            # 获取命令配置
            command_config = self.config_parser.get_command_config(self.config, command_name)
            if not command_config:
                raise ValueError(f"命令不存在: {command_name}")
            
            # 构造请求
            request_bytes = self._build_request(command_config, extra_params)
            logger.info(f"发送命令: {request_bytes.hex().upper()}")
            
            # 发送命令并接收响应
            timeout = command_config.get('timeout', 3.0)
            retry_count = command_config.get('retry_count', 1)

            # 检查是否有期望响应配置
            expected_response = self.config_parser.get_expected_response(self.config, command_name)

            if expected_response:
                # 使用新的应答验证机制
                response_result = self._send_with_validation(request_bytes, expected_response, timeout, retry_count)
                logger.info(f"接收响应: {response_result}")

                # 检查响应是否匹配
                if not response_result.get('success', False):
                    # 详细的错误信息
                    actual_data = response_result.get('data')
                    if isinstance(actual_data, bytes):
                        actual_hex = actual_data.hex().upper()
                        error_msg = f"响应验证失败: 期望 {expected_response}, 实际收到 {actual_hex}"
                    else:
                        error_msg = f"响应验证失败: 期望 {expected_response}, 实际收到 {actual_data}"

                    logger.error(error_msg)

                    # 更新错误统计
                    self.stats['validation_failures'] = self.stats.get('validation_failures', 0) + 1

                    raise ConnectionError(error_msg)

                response_bytes = response_result.get('data')
                # 确保response_bytes是bytes类型
                if isinstance(response_bytes, str):
                    # 如果是字符串，转换为bytes
                    response_bytes = response_bytes.encode('latin-1')
                elif not isinstance(response_bytes, bytes):
                    # 如果是其他类型，尝试转换
                    response_bytes = bytes(response_bytes)

                logger.info(f"响应验证成功: {response_bytes.hex().upper()}")
                # 更新成功统计
                self.stats['validation_successes'] = self.stats.get('validation_successes', 0) + 1
            else:
                # 使用原有的发送机制（兼容性）
                response_bytes = self._send_with_retry(request_bytes, timeout, retry_count)
                logger.info(f"接收响应: {response_bytes.hex().upper()}")

            # 解析响应
            parsed_data = self._parse_response(command_config, response_bytes)

            # 更新统计信息
            self.stats['commands_sent'] += 1
            self.stats['responses_received'] += 1
            self.stats['last_communication'] = time.time()

            # 构建返回结果，确保包含success字段
            result = {
                'success': True,
                'data': parsed_data,
                'raw_response': response_bytes
            }

            logger.info(f"命令执行成功: {parsed_data}")
            return result
            
        except Exception as e:
            logger.error(f"命令执行失败: {e}")
            self.stats['errors'] += 1

            # 如果是验证失败，记录详细信息
            if "响应验证失败" in str(e):
                self.stats['last_validation_error'] = {
                    'command': command_name,
                    'error': str(e),
                    'timestamp': time.time()
                }

            raise
    
    def list_commands(self) -> List[str]:
        """
        列出可用命令
        
        Returns:
            命令名称列表
        """
        return self.config_parser.list_available_commands(self.config)
    
    def get_command_info(self, command_name: str) -> Optional[Dict[str, Any]]:
        """
        获取命令信息
        
        Args:
            command_name: 命令名称
            
        Returns:
            命令信息字典
        """
        command_config = self.config_parser.get_command_config(self.config, command_name)
        if not command_config:
            return None
        
        return {
            'name': command_name,
            'description': command_config.get('description', ''),
            'timeout': command_config.get('timeout', 3.0),
            'retry_count': command_config.get('retry_count', 1),
            'parameters': command_config.get('request', {}).get('parameters', {}),
            'data_fields': list(command_config.get('response', {}).get('data_parsing', {}).keys()),
            'has_expected_response': self.config_parser.has_expected_response(self.config, command_name),
            'expected_response': self.config_parser.get_expected_response(self.config, command_name)
        }
    
    def get_device_info(self) -> Dict[str, Any]:
        """
        获取设备信息
        
        Returns:
            设备信息字典
        """
        return {
            **self.device_config,
            'connection_status': self.is_connected(),
            'available_commands': self.list_commands(),
            'statistics': self.stats.copy(),
            'validation_stats': self.get_validation_stats()
        }

    def get_validation_stats(self) -> Dict[str, Any]:
        """
        获取响应验证统计信息

        Returns:
            验证统计信息字典
        """
        return {
            'validation_successes': self.stats.get('validation_successes', 0),
            'validation_failures': self.stats.get('validation_failures', 0),
            'last_validation_error': self.stats.get('last_validation_error'),
            'validation_success_rate': self._calculate_validation_success_rate()
        }

    def _calculate_validation_success_rate(self) -> float:
        """
        计算验证成功率

        Returns:
            成功率（0.0-1.0）
        """
        successes = self.stats.get('validation_successes', 0)
        failures = self.stats.get('validation_failures', 0)
        total = successes + failures

        if total == 0:
            return 0.0

        return successes / total

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取通信统计信息
        
        Returns:
            统计信息字典
        """
        stats = self.stats.copy()
        if stats['last_communication']:
            stats['last_communication_time'] = time.strftime(
                '%Y-%m-%d %H:%M:%S', 
                time.localtime(stats['last_communication'])
            )
        return stats
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.stats = {
            'commands_sent': 0,
            'responses_received': 0,
            'errors': 0,
            'last_communication': None
        }
        logger.info("统计信息已重置")
    
    def _configure_serial(self) -> None:
        """配置串口参数"""
        connection_config = self.config.get('connection', {})
        
        # 设置串口配置
        serial_config = {
            'baudrate': connection_config.get('baudrate', 9600),
            'bytesize': connection_config.get('bytesize', 8),
            'parity': connection_config.get('parity', 'N'),
            'stopbits': connection_config.get('stopbits', 1),
            'timeout': connection_config.get('timeout', 2.0),
            'read_mode': 'raw'  # 十六进制协议使用原始模式
        }
        
        # 设置串口端口
        port = connection_config.get('port', 'COM1')
        self.serial_protocol = SerialProtocol(port)
        self.serial_protocol.set_config(serial_config)
        
        logger.debug(f"串口配置: {port}, {serial_config}")
    
    def _build_request(self, command_config: Dict[str, Any], extra_params: Dict[str, Any]) -> bytes:
        """
        构造请求数据
        
        Args:
            command_config: 命令配置
            extra_params: 额外参数
            
        Returns:
            请求字节数组
        """
        request_config = command_config['request']
        template = request_config['template']
        parameters = {**request_config['parameters'], **extra_params}
        
        # 验证模板和参数
        if not self.template_engine.validate_template(template, parameters):
            raise ValueError("模板或参数验证失败")
        
        # 渲染模板
        return self.template_engine.render_template(template, parameters)
    
    def _parse_response(self, command_config: Dict[str, Any], response: bytes) -> Dict[str, Any]:
        """
        解析响应数据
        
        Args:
            command_config: 命令配置
            response: 响应字节数组
            
        Returns:
            解析后的数据字典
        """
        response_config = command_config['response']
        return self.template_engine.parse_response(response, response_config)
    
    def _send_with_retry(self, request: bytes, timeout: float, retry_count: int) -> bytes:
        """
        带重试的发送命令
        
        Args:
            request: 请求数据
            timeout: 超时时间
            retry_count: 重试次数
            
        Returns:
            响应数据
            
        Raises:
            ConnectionError: 通信失败
        """
        last_error = None
        
        for attempt in range(retry_count):
            try:
                if attempt > 0:
                    logger.warning(f"重试发送命令 (第{attempt + 1}次)")
                    time.sleep(0.1)  # 短暂延迟
                
                response = self.serial_protocol.send_command(
                    request,
                    wait_response=True,
                    timeout=timeout
                )
                
                if response:
                    return response
                else:
                    raise ConnectionError("未收到响应")
                    
            except Exception as e:
                last_error = e
                logger.warning(f"发送命令失败 (第{attempt + 1}次): {e}")
        
        # 所有重试都失败
        raise ConnectionError(f"发送命令失败，已重试{retry_count}次: {last_error}")

    def _send_with_validation(self, request: bytes, expected_response: str,
                            timeout: float, retry_count: int) -> Dict[str, Any]:
        """
        带应答验证的发送命令

        Args:
            request: 请求数据
            expected_response: 期望的响应数据（十六进制字符串格式）
            timeout: 超时时间
            retry_count: 重试次数

        Returns:
            响应结果字典，包含data、success、matched等字段

        Raises:
            ConnectionError: 通信失败
        """
        last_error = None

        for attempt in range(retry_count):
            try:
                if attempt > 0:
                    logger.warning(f"重试发送命令 (第{attempt + 1}次)")
                    time.sleep(0.1)  # 短暂延迟

                # 使用新的应答验证机制发送命令
                response_result = self.serial_protocol.send_command(
                    request,
                    wait_response=True,
                    timeout=timeout,
                    expected_response=expected_response
                )

                if response_result:
                    if isinstance(response_result, dict):
                        # 新的响应格式，包含匹配信息
                        if response_result.get('success', False):
                            logger.debug(f"响应验证成功 (第{attempt + 1}次)")
                            return response_result
                        else:
                            logger.warning(f"响应验证失败 (第{attempt + 1}次): 期望 {expected_response}, 实际 {response_result.get('data')}")
                            last_error = ConnectionError(f"响应验证失败: 期望 {expected_response}")
                    else:
                        # 兼容旧格式，手动验证
                        if self._manual_response_validation(response_result, expected_response):
                            logger.debug(f"手动响应验证成功 (第{attempt + 1}次)")
                            return {
                                'data': response_result,
                                'success': True,
                                'matched': True,
                                'expected_response': expected_response
                            }
                        else:
                            logger.warning(f"手动响应验证失败 (第{attempt + 1}次)")
                            last_error = ConnectionError(f"响应验证失败: 期望 {expected_response}")
                else:
                    last_error = ConnectionError("未收到响应")

            except Exception as e:
                last_error = e
                logger.warning(f"发送命令失败 (第{attempt + 1}次): {e}")

        # 所有重试都失败
        raise ConnectionError(f"发送命令失败，已重试{retry_count}次: {last_error}")

    def _manual_response_validation(self, received_data: Any, expected_response: str) -> bool:
        """
        手动验证响应数据（兼容性方法）

        Args:
            received_data: 接收到的数据
            expected_response: 期望的响应（十六进制字符串格式）

        Returns:
            是否匹配成功
        """
        try:
            # 将期望响应转换为字节数据
            expected_hex = expected_response.replace(' ', '').upper()
            expected_bytes = bytes.fromhex(expected_hex)

            # 处理接收到的数据
            if isinstance(received_data, bytes):
                received_bytes = received_data
            elif isinstance(received_data, str):
                # 如果是字符串，尝试解析为十六进制
                try:
                    received_hex = received_data.replace(' ', '').upper()
                    received_bytes = bytes.fromhex(received_hex)
                except ValueError:
                    # 如果不是十六进制字符串，编码为字节
                    received_bytes = received_data.encode('utf-8')
            else:
                # 其他类型，转换为字符串再编码
                received_bytes = str(received_data).encode('utf-8')

            # 进行字节级精确匹配
            return received_bytes == expected_bytes

        except Exception as e:
            logger.error(f"手动响应验证过程中发生错误: {e}")
            return False

    # ==================== 订阅管理功能 ====================

    def start_subscription(self, subscription_name: str, callback: Optional[Callable] = None,
                          max_data_count: int = 1000) -> bool:
        """
        启动数据订阅

        Args:
            subscription_name: 订阅名称（对应配置中的命令名）
            callback: 数据回调函数，接收解析后的数据
            max_data_count: 最大数据缓存数量

        Returns:
            启动结果

        Raises:
            ValueError: 订阅配置不存在或无效
            ConnectionError: 设备未连接
        """
        if not self.is_connected():
            raise ConnectionError("设备未连接，无法启动订阅")

        # 检查订阅配置
        subscription_config = self.config_parser.get_command_config(self.config, subscription_name)
        if not subscription_config:
            raise ValueError(f"订阅配置不存在: {subscription_name}")

        if subscription_config.get('type') != 'continuous':
            raise ValueError(f"命令 {subscription_name} 不是持续类型，无法启动订阅")

        with self._subscription_lock:
            # 检查是否已经订阅
            if subscription_name in self.subscriptions:
                logger.warning(f"订阅 {subscription_name} 已存在，先停止现有订阅")
                self._stop_subscription_internal(subscription_name)

            # 创建停止事件
            stop_event = threading.Event()
            self.subscription_stop_events[subscription_name] = stop_event

            # 保存订阅信息
            self.subscriptions[subscription_name] = {
                'config': subscription_config,
                'start_time': datetime.now(),
                'data_count': 0,
                'max_data_count': max_data_count,
                'status': 'starting'
            }

            if callback:
                self.subscription_callbacks[subscription_name] = callback

            # 启动订阅线程
            thread = threading.Thread(
                target=self._subscription_worker,
                args=(subscription_name, stop_event),
                daemon=True,
                name=f"Subscription-{subscription_name}"
            )

            self.subscription_threads[subscription_name] = thread
            thread.start()

            logger.info(f"订阅 {subscription_name} 已启动")
            return True

    def stop_subscription(self, subscription_name: str) -> bool:
        """
        停止数据订阅

        Args:
            subscription_name: 订阅名称

        Returns:
            停止结果
        """
        with self._subscription_lock:
            return self._stop_subscription_internal(subscription_name)

    def _stop_subscription_internal(self, subscription_name: str) -> bool:
        """
        内部停止订阅方法（不加锁）

        Args:
            subscription_name: 订阅名称

        Returns:
            停止结果
        """
        if subscription_name not in self.subscriptions:
            logger.warning(f"订阅 {subscription_name} 不存在")
            return False

        # 设置停止事件
        if subscription_name in self.subscription_stop_events:
            self.subscription_stop_events[subscription_name].set()

        # 等待线程结束
        if subscription_name in self.subscription_threads:
            thread = self.subscription_threads[subscription_name]
            if thread.is_alive():
                thread.join(timeout=2.0)  # 最多等待2秒
                if thread.is_alive():
                    logger.warning(f"订阅线程 {subscription_name} 未能正常结束")

        # 清理资源
        self.subscriptions.pop(subscription_name, None)
        self.subscription_callbacks.pop(subscription_name, None)
        self.subscription_threads.pop(subscription_name, None)
        self.subscription_stop_events.pop(subscription_name, None)

        logger.info(f"订阅 {subscription_name} 已停止")
        return True

    def get_subscription_data(self, subscription_name: str, count: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取订阅数据

        Args:
            subscription_name: 订阅名称
            count: 获取数据数量，None表示获取所有数据

        Returns:
            订阅数据列表
        """
        if subscription_name not in self.subscriptions:
            logger.warning(f"订阅 {subscription_name} 不存在")
            return []

        data_list = self.subscription_data[subscription_name]

        if count is None:
            return data_list.copy()
        else:
            return data_list[-count:] if count > 0 else []

    def clear_subscription_data(self, subscription_name: str) -> bool:
        """
        清空订阅数据缓存

        Args:
            subscription_name: 订阅名称

        Returns:
            清空结果
        """
        if subscription_name not in self.subscriptions:
            return False

        self.subscription_data[subscription_name].clear()
        logger.info(f"订阅 {subscription_name} 数据缓存已清空")
        return True

    def get_subscription_status(self, subscription_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取订阅状态

        Args:
            subscription_name: 订阅名称，None表示获取所有订阅状态

        Returns:
            订阅状态信息
        """
        if subscription_name:
            if subscription_name in self.subscriptions:
                subscription_info = self.subscriptions[subscription_name].copy()
                subscription_info['data_count'] = len(self.subscription_data[subscription_name])
                subscription_info['has_callback'] = subscription_name in self.subscription_callbacks
                return {subscription_name: subscription_info}
            else:
                return {}
        else:
            # 返回所有订阅状态
            status = {}
            for name, info in self.subscriptions.items():
                subscription_info = info.copy()
                subscription_info['data_count'] = len(self.subscription_data[name])
                subscription_info['has_callback'] = name in self.subscription_callbacks
                status[name] = subscription_info
            return status

    def stop_all_subscriptions(self) -> int:
        """
        停止所有订阅

        Returns:
            停止的订阅数量
        """
        with self._subscription_lock:
            subscription_names = list(self.subscriptions.keys())
            stopped_count = 0

            for name in subscription_names:
                if self._stop_subscription_internal(name):
                    stopped_count += 1

            logger.info(f"已停止 {stopped_count} 个订阅")
            return stopped_count

    def _subscription_worker(self, subscription_name: str, stop_event: threading.Event):
        """
        订阅工作线程

        Args:
            subscription_name: 订阅名称
            stop_event: 停止事件
        """
        logger.info(f"订阅工作线程 {subscription_name} 已启动")

        try:
            # 更新订阅状态
            self.subscriptions[subscription_name]['status'] = 'running'

            # 获取订阅配置
            subscription_config = self.subscriptions[subscription_name]['config']
            max_data_count = self.subscriptions[subscription_name]['max_data_count']

            # 持续接收数据
            while not stop_event.is_set():
                try:
                    # 从串口读取数据
                    raw_data = self.serial_protocol.read_data(timeout=1.0)

                    if raw_data:
                        # 解析数据
                        try:
                            parsed_data = self.template_engine.parse_response(
                                raw_data,
                                subscription_config.get('response', {})
                            )

                            # 添加时间戳
                            parsed_data['timestamp'] = datetime.now().isoformat()
                            parsed_data['subscription_name'] = subscription_name

                            # 存储数据
                            data_list = self.subscription_data[subscription_name]
                            data_list.append(parsed_data)

                            # 限制数据数量
                            if len(data_list) > max_data_count:
                                data_list.pop(0)  # 移除最旧的数据

                            # 更新统计
                            self.subscriptions[subscription_name]['data_count'] += 1

                            # 调用回调函数
                            if subscription_name in self.subscription_callbacks:
                                try:
                                    callback = self.subscription_callbacks[subscription_name]
                                    callback(parsed_data)
                                except Exception as e:
                                    logger.error(f"订阅回调函数执行失败 {subscription_name}: {e}")

                            logger.debug(f"订阅 {subscription_name} 接收到数据: {len(raw_data)} 字节")

                        except Exception as e:
                            logger.warning(f"订阅 {subscription_name} 数据解析失败: {e}")
                            # 继续运行，不因解析错误而停止订阅

                except Exception as e:
                    if not stop_event.is_set():
                        logger.error(f"订阅 {subscription_name} 数据接收失败: {e}")
                        time.sleep(0.1)  # 短暂延迟后重试

        except Exception as e:
            logger.error(f"订阅工作线程 {subscription_name} 异常: {e}")

        finally:
            # 更新订阅状态
            if subscription_name in self.subscriptions:
                self.subscriptions[subscription_name]['status'] = 'stopped'

            logger.info(f"订阅工作线程 {subscription_name} 已结束")
