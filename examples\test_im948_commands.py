#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IM948指令测试脚本

测试IM948协议指令的生成和格式是否正确

Author: Augment Agent
Date: 2025-08-02
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.hardware.protocols.hex_template_engine import HexTemplateEngine
from src.hardware.protocols.protocol_config_parser import ProtocolConfigParser


def test_command_generation():
    """测试指令生成"""
    print("=== IM948指令生成测试 ===")
    
    # 加载配置
    config_path = Path(__file__).parent / "im948_config.json"
    parser = ProtocolConfigParser()
    
    with open(config_path, 'r', encoding='utf-8') as f:
        import json
        config = json.load(f)
    
    # 初始化模板引擎
    engine = HexTemplateEngine()
    
    # 测试订阅指令
    print("\n1. 测试订阅指令:")
    expected_subscription = "49 00 0B 12 05 FF 00 04 1E 01 03 05 C0 00 0C 4D"
    
    subscription_cmd = config['protocol']['commands']['set_subscription']
    template = subscription_cmd['request']['template']
    parameters = subscription_cmd['request']['parameters']
    
    print(f"   模板: {template}")
    print(f"   参数: {parameters}")
    
    try:
        generated = engine.render_template(template, parameters)
        generated_hex = ' '.join([f'{b:02X}' for b in generated])
        print(f"   生成指令: {generated_hex}")
        print(f"   期望指令: {expected_subscription}")
        
        if generated_hex == expected_subscription:
            print("   ✅ 订阅指令生成正确")
        else:
            print("   ❌ 订阅指令生成错误")
    except Exception as e:
        print(f"   ❌ 订阅指令生成异常: {e}")
    
    # 测试启动指令
    print("\n2. 测试启动指令:")
    expected_start = "49 00 01 19 1A 4D"
    
    start_cmd = config['protocol']['commands']['start_subscription']
    template = start_cmd['request']['template']
    parameters = start_cmd['request']['parameters']
    
    print(f"   模板: {template}")
    print(f"   参数: {parameters}")
    
    try:
        generated = engine.render_template(template, parameters)
        generated_hex = ' '.join([f'{b:02X}' for b in generated])
        print(f"   生成指令: {generated_hex}")
        print(f"   期望指令: {expected_start}")
        
        if generated_hex == expected_start:
            print("   ✅ 启动指令生成正确")
        else:
            print("   ❌ 启动指令生成错误")
    except Exception as e:
        print(f"   ❌ 启动指令生成异常: {e}")
    
    # 测试响应模板解析
    print("\n3. 测试响应模板解析:")
    euler_cmd = config['protocol']['commands']['get_euler_angles']
    response_template = euler_cmd['response']['template']
    print(f"   响应模板: {response_template}")
    
    # 模拟响应数据
    test_response = "49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D"
    print(f"   测试响应: {test_response}")
    
    try:
        # 这里只是验证模板格式，实际解析需要完整的数据解析引擎
        fields = euler_cmd['response']['fields']
        print(f"   响应字段数量: {len(fields)}")
        
        expected_fields = ['subscription_mask', 'timestamp', 'roll', 'pitch', 'yaw', 'x', 'y', 'z']
        for field in expected_fields:
            if field in fields:
                field_config = fields[field]
                print(f"   ✅ 字段 '{field}': {field_config['type']}, scale={field_config.get('scale', 1.0)}")
            else:
                print(f"   ❌ 缺少字段: {field}")
        
    except Exception as e:
        print(f"   ❌ 响应模板解析异常: {e}")


def test_checksum_calculation():
    """测试校验和计算"""
    print("\n=== 校验和计算测试 ===")
    
    # 测试订阅指令校验和
    print("\n1. 订阅指令校验和:")
    data_bytes = [0x00, 0x0B, 0x12, 0x05, 0xFF, 0x00, 0x04, 0x1E, 0x01, 0x03, 0x05, 0xC0, 0x00, 0x0C]
    calculated_sum = sum(data_bytes) & 0xFF
    print(f"   数据字节: {' '.join([f'{b:02X}' for b in data_bytes])}")
    print(f"   计算校验和: {calculated_sum:02X}")
    print(f"   期望校验和: 0C (用户提供)")
    
    # 测试启动指令校验和
    print("\n2. 启动指令校验和:")
    data_bytes = [0x00, 0x01, 0x19]
    calculated_sum = sum(data_bytes) & 0xFF
    print(f"   数据字节: {' '.join([f'{b:02X}' for b in data_bytes])}")
    print(f"   计算校验和: {calculated_sum:02X}")
    print(f"   期望校验和: 1A")
    
    if calculated_sum == 0x1A:
        print("   ✅ 启动指令校验和正确")
    else:
        print("   ❌ 启动指令校验和错误")


if __name__ == "__main__":
    test_command_generation()
    test_checksum_calculation()
    print("\n=== 测试完成 ===")
