#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据流处理器模块

提供持续数据流的缓存、解析和分发功能。
支持多种数据流处理策略，包括实时处理、批量处理和缓存管理。

Author: Augment Agent
Date: 2025-08-01
"""

import logging
import threading
import time
from typing import Any, Dict, List, Optional, Callable, Union
from datetime import datetime, timedelta
from collections import deque, defaultdict
from enum import Enum
import statistics

# 获取logger
logger = logging.getLogger(__name__)


class ProcessingMode(Enum):
    """数据处理模式枚举"""
    REAL_TIME = "real_time"      # 实时处理
    BATCH = "batch"              # 批量处理
    BUFFERED = "buffered"        # 缓冲处理


class DataStreamProcessor:
    """
    数据流处理器
    
    提供持续数据流的处理功能，包括：
    - 数据缓存和队列管理
    - 实时数据解析和转换
    - 数据分发和回调机制
    - 统计分析和质量监控
    """
    
    def __init__(self, max_buffer_size: int = 10000, 
                 processing_mode: ProcessingMode = ProcessingMode.REAL_TIME):
        """
        初始化数据流处理器
        
        Args:
            max_buffer_size: 最大缓冲区大小
            processing_mode: 数据处理模式
        """
        self.max_buffer_size = max_buffer_size
        self.processing_mode = processing_mode
        
        # 数据缓冲区
        self.data_buffers = defaultdict(lambda: deque(maxlen=max_buffer_size))
        self.processed_data = defaultdict(lambda: deque(maxlen=max_buffer_size))
        
        # 处理器配置
        self.processors = {}  # {stream_name: processor_config}
        self.data_parsers = {}  # {stream_name: parser_function}
        self.data_filters = {}  # {stream_name: filter_function}
        self.data_callbacks = defaultdict(list)  # {stream_name: [callback_functions]}
        
        # 线程安全
        self._lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'total_data_received': 0,
            'total_data_processed': 0,
            'total_data_filtered': 0,
            'processing_errors': 0,
            'active_streams': 0
        }
        
        # 质量监控
        self.quality_metrics = defaultdict(lambda: {
            'data_rate': 0.0,           # 数据接收速率 (条/秒)
            'processing_rate': 0.0,     # 数据处理速率 (条/秒)
            'error_rate': 0.0,          # 错误率
            'latency': 0.0,             # 处理延迟 (毫秒)
            'last_update': None
        })
        
        logger.info(f"数据流处理器已初始化，模式: {processing_mode.value}")
    
    def register_stream(self, stream_name: str, config: Dict[str, Any]) -> bool:
        """
        注册数据流
        
        Args:
            stream_name: 数据流名称
            config: 数据流配置
            
        Returns:
            注册结果
        """
        with self._lock:
            if stream_name in self.processors:
                logger.warning(f"数据流 {stream_name} 已存在，将覆盖现有配置")
            
            # 保存处理器配置
            self.processors[stream_name] = {
                'name': stream_name,
                'config': config,
                'created_time': datetime.now(),
                'status': 'registered'
            }
            
            # 设置数据解析器
            if 'parser' in config:
                self.data_parsers[stream_name] = config['parser']
            
            # 设置数据过滤器
            if 'filter' in config:
                self.data_filters[stream_name] = config['filter']
            
            self.stats['active_streams'] += 1
            
            logger.info(f"数据流 {stream_name} 已注册")
            return True
    
    def unregister_stream(self, stream_name: str) -> bool:
        """
        注销数据流
        
        Args:
            stream_name: 数据流名称
            
        Returns:
            注销结果
        """
        with self._lock:
            if stream_name not in self.processors:
                logger.warning(f"数据流 {stream_name} 不存在")
                return False
            
            # 清理资源
            del self.processors[stream_name]
            self.data_parsers.pop(stream_name, None)
            self.data_filters.pop(stream_name, None)
            self.data_callbacks.pop(stream_name, None)
            
            # 清理数据缓冲区
            self.data_buffers.pop(stream_name, None)
            self.processed_data.pop(stream_name, None)
            self.quality_metrics.pop(stream_name, None)
            
            self.stats['active_streams'] = max(0, self.stats['active_streams'] - 1)
            
            logger.info(f"数据流 {stream_name} 已注销")
            return True
    
    def add_callback(self, stream_name: str, callback: Callable) -> bool:
        """
        添加数据回调函数
        
        Args:
            stream_name: 数据流名称
            callback: 回调函数
            
        Returns:
            添加结果
        """
        if stream_name not in self.processors:
            logger.error(f"数据流 {stream_name} 不存在")
            return False
        
        self.data_callbacks[stream_name].append(callback)
        logger.info(f"数据流 {stream_name} 添加回调函数")
        return True
    
    def remove_callback(self, stream_name: str, callback: Callable) -> bool:
        """
        移除数据回调函数
        
        Args:
            stream_name: 数据流名称
            callback: 回调函数
            
        Returns:
            移除结果
        """
        if stream_name not in self.data_callbacks:
            return False
        
        try:
            self.data_callbacks[stream_name].remove(callback)
            logger.info(f"数据流 {stream_name} 移除回调函数")
            return True
        except ValueError:
            logger.warning(f"数据流 {stream_name} 回调函数不存在")
            return False
    
    def process_data(self, stream_name: str, raw_data: Any) -> Optional[Dict[str, Any]]:
        """
        处理数据
        
        Args:
            stream_name: 数据流名称
            raw_data: 原始数据
            
        Returns:
            处理后的数据，None表示处理失败或被过滤
        """
        if stream_name not in self.processors:
            logger.error(f"数据流 {stream_name} 未注册")
            return None
        
        start_time = time.time()
        
        try:
            # 更新统计
            self.stats['total_data_received'] += 1
            
            # 添加到原始数据缓冲区
            self.data_buffers[stream_name].append({
                'data': raw_data,
                'timestamp': datetime.now(),
                'stream_name': stream_name
            })
            
            # 数据解析
            parsed_data = self._parse_data(stream_name, raw_data)
            if parsed_data is None:
                return None
            
            # 数据过滤
            if not self._filter_data(stream_name, parsed_data):
                self.stats['total_data_filtered'] += 1
                return None
            
            # 数据增强
            enhanced_data = self._enhance_data(stream_name, parsed_data, start_time)
            
            # 添加到处理后数据缓冲区
            self.processed_data[stream_name].append(enhanced_data)
            
            # 更新统计
            self.stats['total_data_processed'] += 1
            
            # 更新质量指标
            self._update_quality_metrics(stream_name, start_time)
            
            # 执行回调
            self._execute_callbacks(stream_name, enhanced_data)
            
            return enhanced_data
            
        except Exception as e:
            logger.error(f"数据流 {stream_name} 处理失败: {e}")
            self.stats['processing_errors'] += 1
            return None
    
    def get_buffer_data(self, stream_name: str, data_type: str = 'processed', 
                       count: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取缓冲区数据
        
        Args:
            stream_name: 数据流名称
            data_type: 数据类型 ('raw' 或 'processed')
            count: 获取数据数量，None表示获取所有数据
            
        Returns:
            数据列表
        """
        if stream_name not in self.processors:
            return []
        
        if data_type == 'raw':
            data_buffer = self.data_buffers[stream_name]
        else:
            data_buffer = self.processed_data[stream_name]
        
        if count is None:
            return list(data_buffer)
        else:
            return list(data_buffer)[-count:] if count > 0 else []
    
    def clear_buffer(self, stream_name: str, data_type: str = 'both') -> bool:
        """
        清空缓冲区
        
        Args:
            stream_name: 数据流名称
            data_type: 数据类型 ('raw', 'processed', 'both')
            
        Returns:
            清空结果
        """
        if stream_name not in self.processors:
            return False
        
        if data_type in ['raw', 'both']:
            self.data_buffers[stream_name].clear()
        
        if data_type in ['processed', 'both']:
            self.processed_data[stream_name].clear()
        
        logger.info(f"数据流 {stream_name} 缓冲区已清空 ({data_type})")
        return True
    
    def get_statistics(self, stream_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取统计信息
        
        Args:
            stream_name: 数据流名称，None表示获取全局统计
            
        Returns:
            统计信息
        """
        with self._lock:
            if stream_name:
                if stream_name in self.processors:
                    return {
                        'stream_name': stream_name,
                        'buffer_size': len(self.data_buffers[stream_name]),
                        'processed_size': len(self.processed_data[stream_name]),
                        'quality_metrics': self.quality_metrics[stream_name].copy(),
                        'callbacks_count': len(self.data_callbacks[stream_name])
                    }
                else:
                    return {}
            else:
                return self.stats.copy()
    
    def get_quality_metrics(self, stream_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取质量指标
        
        Args:
            stream_name: 数据流名称，None表示获取所有流的质量指标
            
        Returns:
            质量指标
        """
        if stream_name:
            return self.quality_metrics.get(stream_name, {}).copy()
        else:
            return {name: metrics.copy() for name, metrics in self.quality_metrics.items()}

    # ==================== 内部处理方法 ====================

    def _parse_data(self, stream_name: str, raw_data: Any) -> Optional[Dict[str, Any]]:
        """
        解析原始数据

        Args:
            stream_name: 数据流名称
            raw_data: 原始数据

        Returns:
            解析后的数据，None表示解析失败
        """
        try:
            # 如果有自定义解析器，使用自定义解析器
            if stream_name in self.data_parsers:
                parser = self.data_parsers[stream_name]
                return parser(raw_data)

            # 默认解析逻辑
            if isinstance(raw_data, dict):
                return raw_data.copy()
            elif isinstance(raw_data, (bytes, bytearray)):
                return {
                    'raw_bytes': raw_data,
                    'hex_string': raw_data.hex().upper(),
                    'length': len(raw_data)
                }
            elif isinstance(raw_data, str):
                return {
                    'raw_string': raw_data,
                    'length': len(raw_data)
                }
            else:
                return {
                    'raw_data': raw_data,
                    'data_type': type(raw_data).__name__
                }

        except Exception as e:
            logger.error(f"数据解析失败 {stream_name}: {e}")
            return None

    def _filter_data(self, stream_name: str, data: Dict[str, Any]) -> bool:
        """
        过滤数据

        Args:
            stream_name: 数据流名称
            data: 数据

        Returns:
            是否通过过滤
        """
        try:
            # 如果有自定义过滤器，使用自定义过滤器
            if stream_name in self.data_filters:
                filter_func = self.data_filters[stream_name]
                return filter_func(data)

            # 默认过滤逻辑：通过所有数据
            return True

        except Exception as e:
            logger.error(f"数据过滤失败 {stream_name}: {e}")
            return False

    def _enhance_data(self, stream_name: str, data: Dict[str, Any], start_time: float) -> Dict[str, Any]:
        """
        增强数据

        Args:
            stream_name: 数据流名称
            data: 原始数据
            start_time: 处理开始时间

        Returns:
            增强后的数据
        """
        enhanced_data = data.copy()

        # 添加元数据
        enhanced_data.update({
            'stream_name': stream_name,
            'timestamp': datetime.now().isoformat(),
            'processing_time': (time.time() - start_time) * 1000,  # 毫秒
            'sequence_id': self.stats['total_data_processed'] + 1
        })

        return enhanced_data

    def _update_quality_metrics(self, stream_name: str, start_time: float):
        """
        更新质量指标

        Args:
            stream_name: 数据流名称
            start_time: 处理开始时间
        """
        try:
            current_time = datetime.now()
            processing_time = (time.time() - start_time) * 1000  # 毫秒

            metrics = self.quality_metrics[stream_name]

            # 更新处理延迟
            if metrics['latency'] == 0.0:
                metrics['latency'] = processing_time
            else:
                # 使用指数移动平均
                metrics['latency'] = 0.9 * metrics['latency'] + 0.1 * processing_time

            # 计算数据速率
            if metrics['last_update']:
                time_diff = (current_time - metrics['last_update']).total_seconds()
                if time_diff > 0:
                    # 简单的速率计算
                    metrics['data_rate'] = 1.0 / time_diff
                    metrics['processing_rate'] = 1.0 / time_diff

            # 计算错误率
            total_received = len(self.data_buffers[stream_name])
            if total_received > 0:
                stream_errors = sum(1 for item in self.data_buffers[stream_name]
                                  if item.get('error', False))
                metrics['error_rate'] = stream_errors / total_received

            metrics['last_update'] = current_time

        except Exception as e:
            logger.error(f"更新质量指标失败 {stream_name}: {e}")

    def _execute_callbacks(self, stream_name: str, data: Dict[str, Any]):
        """
        执行回调函数

        Args:
            stream_name: 数据流名称
            data: 处理后的数据
        """
        callbacks = self.data_callbacks.get(stream_name, [])

        for callback in callbacks:
            try:
                callback(data)
            except Exception as e:
                logger.error(f"回调函数执行失败 {stream_name}: {e}")

    def get_stream_status(self, stream_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取数据流状态

        Args:
            stream_name: 数据流名称，None表示获取所有流状态

        Returns:
            数据流状态信息
        """
        with self._lock:
            if stream_name:
                if stream_name in self.processors:
                    processor_info = self.processors[stream_name].copy()
                    processor_info.update({
                        'buffer_size': len(self.data_buffers[stream_name]),
                        'processed_size': len(self.processed_data[stream_name]),
                        'callbacks_count': len(self.data_callbacks[stream_name]),
                        'quality_metrics': self.quality_metrics[stream_name].copy()
                    })
                    return {stream_name: processor_info}
                else:
                    return {}
            else:
                # 返回所有流状态
                status = {}
                for name, processor_info in self.processors.items():
                    stream_status = processor_info.copy()
                    stream_status.update({
                        'buffer_size': len(self.data_buffers[name]),
                        'processed_size': len(self.processed_data[name]),
                        'callbacks_count': len(self.data_callbacks[name]),
                        'quality_metrics': self.quality_metrics[name].copy()
                    })
                    status[name] = stream_status
                return status

    def cleanup(self):
        """清理所有资源"""
        with self._lock:
            # 清理所有数据流
            stream_names = list(self.processors.keys())
            for stream_name in stream_names:
                self.unregister_stream(stream_name)

            # 重置统计信息
            self.stats = {
                'total_data_received': 0,
                'total_data_processed': 0,
                'total_data_filtered': 0,
                'processing_errors': 0,
                'active_streams': 0
            }

            logger.info("数据流处理器已清理")

    def __del__(self):
        """析构函数"""
        try:
            self.cleanup()
        except Exception as e:
            logger.error(f"数据流处理器析构时出错: {e}")
