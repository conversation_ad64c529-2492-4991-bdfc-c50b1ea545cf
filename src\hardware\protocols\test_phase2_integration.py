#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
阶段2集成测试模块

测试十六进制协议配置框架的阶段2功能，包括：
- 持续数据流支持
- 订阅管理功能
- 一次性指令和持续指令的兼容性
- IM948协议配置示例验证

Author: Augment Agent
Date: 2025-08-01
"""

import unittest
import json
import time
import threading
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import tempfile
import os

# 导入测试目标
from .hex_protocol_adapter import HexProtocolAdapter
from .subscription_manager import SubscriptionManager, SubscriptionStatus
from .data_stream_processor import DataStreamProcessor, ProcessingMode
from .protocol_config_parser import ProtocolConfigParser


class TestPhase2Integration(unittest.TestCase):
    """阶段2集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时配置文件
        self.temp_dir = tempfile.mkdtemp()
        self.config_path = Path(self.temp_dir) / "test_config.json"
        
        # 创建测试配置
        self.test_config = {
            "device_name": "测试设备",
            "device_type": "IMU",
            "description": "测试用惯性测量单元",
            "connection": {
                "port": "COM_TEST",
                "baudrate": 115200,
                "bytesize": 8,
                "parity": "N",
                "stopbits": 1,
                "timeout": 1.0
            },
            "protocol": {
                "type": "hex_template",
                "encoding": "utf-8",
                "commands": {
                    "one_time_cmd": {
                        "type": "one_time",
                        "description": "一次性指令测试",
                        "timeout": 2.0,
                        "retry_count": 3,
                        "request": {
                            "template": "A5 5A 01 01 {checksum:02X}",
                            "parameters": {}
                        },
                        "response": {
                            "template": "A5 5A 02 81 {status:02X} {checksum:02X}",
                            "fields": {
                                "status": {
                                    "type": "uint8",
                                    "description": "状态码"
                                }
                            }
                        }
                    },
                    "continuous_cmd": {
                        "type": "continuous",
                        "description": "持续指令测试",
                        "subscription_config": {
                            "max_data_count": 100,
                            "data_interval": 0.1,
                            "auto_start": False
                        },
                        "request": {
                            "template": "A5 5A 01 02 {checksum:02X}",
                            "parameters": {}
                        },
                        "response": {
                            "template": "A5 5A 06 82 {value:04X} {timestamp:04X} {checksum:02X}",
                            "fields": {
                                "value": {
                                    "type": "int16_be",
                                    "scale": 0.01,
                                    "unit": "V",
                                    "description": "测试值"
                                },
                                "timestamp": {
                                    "type": "uint16_be",
                                    "unit": "ms",
                                    "description": "时间戳"
                                }
                            }
                        }
                    }
                }
            }
        }
        
        # 保存配置文件
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(self.test_config, f, ensure_ascii=False, indent=2)
    
    def tearDown(self):
        """测试后清理"""
        # 清理临时文件
        if self.config_path.exists():
            self.config_path.unlink()
        os.rmdir(self.temp_dir)
    
    def test_config_parser_continuous_support(self):
        """测试配置解析器的持续指令支持"""
        parser = ProtocolConfigParser()
        config = parser.load_config(self.config_path)
        
        # 测试指令类型识别
        self.assertEqual(parser.get_command_type(config, 'one_time_cmd'), 'one_time')
        self.assertEqual(parser.get_command_type(config, 'continuous_cmd'), 'continuous')
        
        # 测试持续指令判断
        self.assertFalse(parser.is_continuous_command(config, 'one_time_cmd'))
        self.assertTrue(parser.is_continuous_command(config, 'continuous_cmd'))
        
        # 测试指令列表获取
        continuous_cmds = parser.get_continuous_commands(config)
        one_time_cmds = parser.get_one_time_commands(config)
        
        self.assertIn('continuous_cmd', continuous_cmds)
        self.assertIn('one_time_cmd', one_time_cmds)
        
        # 测试订阅配置获取
        subscription_config = parser.get_subscription_config(config, 'continuous_cmd')
        self.assertIsNotNone(subscription_config)
        self.assertEqual(subscription_config['max_data_count'], 100)
        self.assertEqual(subscription_config['data_interval'], 0.1)
        
        # 测试订阅验证
        self.assertTrue(parser.validate_command_for_subscription(config, 'continuous_cmd'))
        
        with self.assertRaises(ValueError):
            parser.validate_command_for_subscription(config, 'one_time_cmd')
    
    def test_subscription_manager_lifecycle(self):
        """测试订阅管理器生命周期"""
        manager = SubscriptionManager(max_data_per_subscription=50)
        
        # 测试订阅创建
        config = {"test": "config"}
        callback = Mock()
        
        self.assertTrue(manager.create_subscription("test_stream", config, callback))
        self.assertFalse(manager.create_subscription("test_stream", config))  # 重复创建
        
        # 测试订阅状态
        status = manager.get_subscription_status("test_stream")
        self.assertIn("test_stream", status)
        self.assertEqual(status["test_stream"]["status"], SubscriptionStatus.STOPPED)
        
        # 测试数据源模拟
        data_source = Mock(return_value=b'\xA5\x5A\x06\x82\x01\x23\x00\x45\x67')
        
        # 测试订阅启动
        self.assertTrue(manager.start_subscription("test_stream", data_source))
        time.sleep(0.1)  # 等待线程启动
        
        # 检查状态变化
        status = manager.get_subscription_status("test_stream")
        self.assertEqual(status["test_stream"]["status"], SubscriptionStatus.RUNNING)
        
        # 测试数据接收
        time.sleep(0.2)  # 等待数据处理
        data = manager.get_subscription_data("test_stream", 5)
        self.assertGreater(len(data), 0)
        
        # 测试订阅停止
        self.assertTrue(manager.stop_subscription("test_stream"))
        status = manager.get_subscription_status("test_stream")
        self.assertEqual(status["test_stream"]["status"], SubscriptionStatus.STOPPED)
        
        # 测试订阅移除
        self.assertTrue(manager.remove_subscription("test_stream"))
        status = manager.get_subscription_status("test_stream")
        self.assertEqual(len(status), 0)
    
    def test_data_stream_processor_functionality(self):
        """测试数据流处理器功能"""
        processor = DataStreamProcessor(max_buffer_size=100)
        
        # 测试数据流注册
        config = {
            "parser": lambda data: {"parsed": data.hex()},
            "filter": lambda data: len(data.get("parsed", "")) > 0
        }
        
        self.assertTrue(processor.register_stream("test_stream", config))
        
        # 测试回调添加
        callback = Mock()
        self.assertTrue(processor.add_callback("test_stream", callback))
        
        # 测试数据处理
        test_data = b'\xA5\x5A\x06\x82\x01\x23\x00\x45\x67'
        result = processor.process_data("test_stream", test_data)
        
        self.assertIsNotNone(result)
        self.assertIn("parsed", result)
        self.assertIn("timestamp", result)
        self.assertIn("stream_name", result)
        
        # 验证回调被调用
        callback.assert_called_once()
        
        # 测试缓冲区数据获取
        buffer_data = processor.get_buffer_data("test_stream", "processed", 10)
        self.assertEqual(len(buffer_data), 1)
        
        # 测试统计信息
        stats = processor.get_statistics()
        self.assertEqual(stats["total_data_received"], 1)
        self.assertEqual(stats["total_data_processed"], 1)
        
        # 测试数据流注销
        self.assertTrue(processor.unregister_stream("test_stream"))
    
    @patch('src.hardware.protocols.hex_protocol_adapter.SerialProtocol')
    def test_hex_protocol_adapter_subscription_integration(self, mock_serial):
        """测试协议适配器订阅集成功能"""
        # 模拟串口协议
        mock_serial_instance = Mock()
        mock_serial.return_value = mock_serial_instance
        mock_serial_instance.connect.return_value = True
        mock_serial_instance.is_connected.return_value = True
        mock_serial_instance.read_data.return_value = b'\xA5\x5A\x06\x82\x01\x23\x00\x45\x67'
        
        # 创建协议适配器
        adapter = HexProtocolAdapter(self.config_path)
        
        # 测试连接
        self.assertTrue(adapter.connect())
        
        # 测试订阅启动
        callback = Mock()
        self.assertTrue(adapter.start_subscription('continuous_cmd', callback))
        
        # 等待数据处理
        time.sleep(0.2)
        
        # 测试订阅状态
        status = adapter.get_subscription_status()
        self.assertIn('continuous_cmd', status)
        
        # 测试数据获取
        data = adapter.get_subscription_data('continuous_cmd', 5)
        self.assertGreaterEqual(len(data), 0)
        
        # 测试订阅停止
        self.assertTrue(adapter.stop_subscription('continuous_cmd'))
        
        # 测试断开连接时的清理
        adapter.disconnect()
        status = adapter.get_subscription_status()
        self.assertEqual(len(status), 0)
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        # 创建只包含一次性指令的旧配置
        old_config = {
            "device_name": "旧设备",
            "connection": {"port": "COM1", "baudrate": 9600},
            "protocol": {
                "type": "hex_template",
                "commands": {
                    "old_cmd": {
                        "type": "one_time",  # 明确指定类型以通过验证
                        "description": "旧指令",
                        "request": {
                            "template": "01 02 03 {checksum:02X}",
                            "parameters": {}
                        },
                        "response": {
                            "template": "01 02 {status:02X} {checksum:02X}",
                            "fields": {
                                "status": {"type": "uint8"}
                            }
                        }
                    }
                }
            }
        }
        
        old_config_path = Path(self.temp_dir) / "old_config.json"
        with open(old_config_path, 'w', encoding='utf-8') as f:
            json.dump(old_config, f, ensure_ascii=False, indent=2)
        
        try:
            # 测试配置解析器兼容性
            parser = ProtocolConfigParser()
            config = parser.load_config(old_config_path)
            
            # 默认应该是一次性指令
            self.assertEqual(parser.get_command_type(config, 'old_cmd'), 'one_time')
            self.assertFalse(parser.is_continuous_command(config, 'old_cmd'))
            
            # 测试协议适配器兼容性
            with patch('src.hardware.protocols.hex_protocol_adapter.SerialProtocol'):
                adapter = HexProtocolAdapter(old_config_path)
                # 应该能正常初始化，不会因为缺少type字段而失败
                self.assertIsNotNone(adapter.config)
        
        finally:
            old_config_path.unlink()
    
    def test_im948_config_validation(self):
        """测试IM948配置文件验证"""
        # 加载IM948配置文件
        im948_config_path = Path(__file__).parent.parent.parent.parent / "examples" / "im948_config.json"
        
        if im948_config_path.exists():
            parser = ProtocolConfigParser()
            config = parser.load_config(im948_config_path)
            
            # 验证配置结构
            self.assertIn('device_name', config)
            self.assertIn('connection', config)
            self.assertIn('protocol', config)
            
            # 验证指令类型
            self.assertEqual(parser.get_command_type(config, 'set_subscription'), 'one_time')
            self.assertEqual(parser.get_command_type(config, 'get_euler_angles'), 'continuous')
            self.assertEqual(parser.get_command_type(config, 'get_position_3d'), 'continuous')
            
            # 验证持续指令配置
            euler_subscription = parser.get_subscription_config(config, 'get_euler_angles')
            self.assertIsNotNone(euler_subscription)
            self.assertIn('max_data_count', euler_subscription)
            
            position_subscription = parser.get_subscription_config(config, 'get_position_3d')
            self.assertIsNotNone(position_subscription)
            self.assertIn('data_interval', position_subscription)
            
            # 验证订阅验证功能
            self.assertTrue(parser.validate_command_for_subscription(config, 'get_euler_angles'))
            self.assertTrue(parser.validate_command_for_subscription(config, 'get_position_3d'))
            
            with self.assertRaises(ValueError):
                parser.validate_command_for_subscription(config, 'set_subscription')
    
    def test_error_handling_and_recovery(self):
        """测试错误处理和恢复机制"""
        manager = SubscriptionManager()
        
        # 测试不存在的订阅操作
        self.assertFalse(manager.start_subscription("nonexistent", lambda: None))
        self.assertFalse(manager.stop_subscription("nonexistent"))
        self.assertFalse(manager.remove_subscription("nonexistent"))
        
        # 测试异常数据源
        def error_data_source():
            raise Exception("数据源错误")
        
        manager.create_subscription("error_stream", {})
        manager.start_subscription("error_stream", error_data_source)
        
        time.sleep(0.1)  # 等待错误处理
        
        # 订阅应该仍然存在但状态可能是错误状态
        status = manager.get_subscription_status("error_stream")
        self.assertIn("error_stream", status)
        
        # 测试数据流处理器错误处理
        processor = DataStreamProcessor()

        # 注册一个正常的流
        processor.register_stream("error_stream", {
            "parser": lambda data: {"test": "data"},
            "filter": lambda data: True
        })

        # 通过破坏内部状态来制造异常
        original_enhance = processor._enhance_data
        def error_enhance(*args, **kwargs):
            raise RuntimeError("增强数据时出错")
        processor._enhance_data = error_enhance

        result = processor.process_data("error_stream", b"test")
        self.assertIsNone(result)  # 处理失败应该返回None

        stats = processor.get_statistics()
        self.assertEqual(stats["processing_errors"], 1)
    
    def test_performance_and_scalability(self):
        """测试性能和可扩展性"""
        manager = SubscriptionManager(max_data_per_subscription=1000)
        
        # 创建多个订阅
        num_subscriptions = 5
        for i in range(num_subscriptions):
            stream_name = f"stream_{i}"
            manager.create_subscription(stream_name, {})
            
            # 模拟数据源
            data_source = Mock(return_value=f"data_{i}".encode())
            manager.start_subscription(stream_name, data_source)
        
        # 等待数据处理
        time.sleep(0.5)
        
        # 检查所有订阅状态
        all_status = manager.get_subscription_status()
        self.assertEqual(len(all_status), num_subscriptions)
        
        # 检查统计信息
        stats = manager.get_statistics()
        self.assertEqual(stats['active_subscriptions'], num_subscriptions)
        
        # 停止所有订阅
        stopped_count = manager.stop_all_subscriptions()
        self.assertEqual(stopped_count, num_subscriptions)


def run_integration_tests():
    """运行集成测试"""
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestPhase2Integration)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 返回测试结果
    return result.wasSuccessful()


if __name__ == '__main__':
    print("=" * 60)
    print("十六进制协议配置框架 - 阶段2集成测试")
    print("=" * 60)
    
    success = run_integration_tests()
    
    if success:
        print("\n✅ 所有测试通过！阶段2功能验证成功。")
        print("\n功能验证总结:")
        print("- ✅ 持续数据流支持")
        print("- ✅ 订阅管理功能")
        print("- ✅ 配置解析器扩展")
        print("- ✅ 数据流处理器")
        print("- ✅ 向后兼容性")
        print("- ✅ IM948协议配置")
        print("- ✅ 错误处理机制")
        print("- ✅ 性能和可扩展性")
    else:
        print("\n❌ 部分测试失败，请检查错误信息。")
    
    print("\n" + "=" * 60)
