#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
串口协议模块

实现基于pyserial的串口通信协议适配器。
"""

import logging
import queue
import threading
import time
from typing import Any, Dict, List, Optional, Tuple, Union

try:
    import serial
    from serial.tools import list_ports
except ImportError:
    raise ImportError("未安装pyserial库，请使用pip install pyserial安装")

from src.hardware.protocol_base import (
    ProtocolBase, ProtocolEventType, 
    ConnectionError, TimeoutError, CommandError
)

# 获取logger
logger = logging.getLogger(__name__)

class SerialProtocol(ProtocolBase):
    """
    串口协议适配器
    
    实现基于pyserial的串口通信功能，支持异步数据接收和命令发送。
    """
    
    # 默认串口配置
    DEFAULT_CONFIG = {
        "port": None,         # 串口名称，如"COM1"或"/dev/ttyUSB0"
        "baudrate": 9600,     # 波特率
        "bytesize": 8,        # 数据位 (5, 6, 7, 8)
        "parity": "N",        # 校验位 ("N"=无校验, "E"=偶校验, "O"=奇校验, "M"=标记校验, "S"=空格校验)
        "stopbits": 1,        # 停止位 (1, 1.5, 2)
        "timeout": 1.0,       # 读取超时时间(秒)
        "write_timeout": 1.0, # 写入超时时间(秒)
        "xonxoff": False,     # 是否启用软件流控
        "rtscts": False,      # 是否启用RTS/CTS硬件流控
        "dsrdtr": False,      # 是否启用DSR/DTR硬件流控
        "buffer_size": 4096,  # 接收缓冲区大小
        "eol": b"\n",         # 行结束符
        "encoding": "utf-8",  # 字符编码
        "read_mode": "line"   # 读取模式: "line"按行读取, "raw"读取原始字节
    }
    
    def __init__(self, port: str = None):
        """
        初始化串口协议适配器
        
        Args:
            port: 串口名称，如"COM1"或"/dev/ttyUSB0"
        """
        super().__init__("串口协议")
        
        # 创建初始配置
        config = self.DEFAULT_CONFIG.copy()
        if port:
            config["port"] = port
            
        # 设置配置
        self._config = config
        
        # 创建串口对象
        self._serial = None
        
        # 创建消息队列，用于线程间通信
        self._msg_queue = queue.Queue(maxsize=100)
        
        # 创建响应字典，用于保存命令响应
        self._response_dict = {}
        self._response_lock = threading.Lock()

        # 线程控制变量
        self._is_running = False
        self._read_thread = None

        logger.debug("串口协议适配器已初始化")
    
    @staticmethod
    def list_available_ports() -> List[Tuple[str, str, str]]:
        """
        列出系统上可用的串口（增强版）

        使用多种方法检测串口，包括：
        1. pyserial的标准检测
        2. Windows注册表检测
        3. 直接端口测试

        Returns:
            包含 (端口名, 描述, 硬件ID) 的列表
        """
        ports_dict = {}  # 使用字典避免重复

        # 方法1: 使用pyserial标准检测
        try:
            for port in list_ports.comports():
                ports_dict[port.device] = (port.device, port.description, port.hwid)
                logger.debug(f"pyserial检测到: {port.device} - {port.description}")
        except Exception as e:
            logger.warning(f"pyserial检测失败: {e}")

        # 方法2: Windows注册表检测（仅Windows系统）
        import platform
        if platform.system() == "Windows":
            try:
                import winreg
                # 检查HKEY_LOCAL_MACHINE\HARDWARE\DEVICEMAP\SERIALCOMM
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                   r"HARDWARE\DEVICEMAP\SERIALCOMM") as key:
                    i = 0
                    while True:
                        try:
                            name, value, _ = winreg.EnumValue(key, i)
                            if value not in ports_dict:
                                ports_dict[value] = (value, f"串口设备 ({value})", f"REG:{name}")
                                logger.debug(f"注册表检测到: {value} - {name}")
                            i += 1
                        except OSError:
                            break
            except Exception as e:
                logger.warning(f"注册表检测失败: {e}")

        # 方法3: 直接端口测试（Windows COM1-COM256）
        if platform.system() == "Windows":
            import serial
            for i in range(1, 257):  # 测试COM1到COM256
                port_name = f"COM{i}"
                if port_name not in ports_dict:
                    try:
                        # 尝试打开端口（不实际连接）
                        test_serial = serial.Serial()
                        test_serial.port = port_name
                        test_serial.timeout = 0.1
                        test_serial.open()
                        test_serial.close()
                        # 如果能打开，说明端口存在
                        ports_dict[port_name] = (port_name, f"检测到的串口 ({port_name})", "DIRECT_TEST")
                        logger.debug(f"直接测试检测到: {port_name}")
                    except (serial.SerialException, OSError):
                        # 端口不存在或无法访问，跳过
                        pass
                    except Exception as e:
                        # 其他异常，记录但继续
                        logger.debug(f"测试 {port_name} 时出现异常: {e}")

        # 转换为列表并排序
        result = list(ports_dict.values())
        result.sort(key=lambda x: x[0])  # 按端口名排序

        logger.info(f"总共检测到 {len(result)} 个串口设备")
        return result
    
    def _validate_config(self, config: Dict) -> None:
        """
        验证串口配置
        
        Args:
            config: 要验证的配置字典
            
        Raises:
            ValueError: 如果配置无效
        """
        # 检查必需的配置项
        if "port" in config and config["port"] is None:
            raise ValueError("必须指定串口名称")
            
        # 验证波特率
        if "baudrate" in config:
            valid_baudrates = [110, 300, 600, 1200, 2400, 4800, 9600, 
                              14400, 19200, 38400, 57600, 115200, 230400, 
                              460800, 921600]
            if config["baudrate"] not in valid_baudrates:
                raise ValueError(f"无效的波特率: {config['baudrate']}")
                
        # 验证数据位
        if "bytesize" in config and config["bytesize"] not in [5, 6, 7, 8]:
            raise ValueError(f"无效的数据位: {config['bytesize']}")
            
        # 验证校验位
        if "parity" in config and config["parity"] not in ["N", "E", "O", "M", "S"]:
            raise ValueError(f"无效的校验位: {config['parity']}")
            
        # 验证停止位
        if "stopbits" in config and config["stopbits"] not in [1, 1.5, 2]:
            raise ValueError(f"无效的停止位: {config['stopbits']}")
            
        # 验证超时时间
        if "timeout" in config and (not isinstance(config["timeout"], (int, float)) or config["timeout"] < 0):
            raise ValueError(f"无效的读取超时时间: {config['timeout']}")
            
        # 验证写入超时时间
        if "write_timeout" in config and (not isinstance(config["write_timeout"], (int, float)) or config["write_timeout"] < 0):
            raise ValueError(f"无效的写入超时时间: {config['write_timeout']}")
            
        # 验证读取模式
        if "read_mode" in config and config["read_mode"] not in ["line", "raw"]:
            raise ValueError(f"无效的读取模式: {config['read_mode']}")
            
        logger.debug("串口配置验证通过")
    
    def connect(self) -> bool:
        """
        连接到串口
        
        Returns:
            连接是否成功
            
        Raises:
            ConnectionError: 连接失败时抛出
        """
        if self._is_connected:
            logger.warning("串口已经连接")
            return True
            
        try:
            # 验证配置
            self._validate_config(self._config)
            
            # 获取配置
            port = self._config["port"]
            baudrate = self._config["baudrate"]
            bytesize = self._config["bytesize"]
            parity = self._config["parity"]
            stopbits = self._config["stopbits"]
            timeout = self._config["timeout"]
            write_timeout = self._config["write_timeout"]
            xonxoff = self._config["xonxoff"]
            rtscts = self._config["rtscts"]
            dsrdtr = self._config["dsrdtr"]
            
            # 确保端口名称已指定
            if port is None:
                raise ConnectionError("未指定串口名称")
                
            # 创建串口对象
            self._serial = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=getattr(serial, f"EIGHTBITS" if bytesize == 8 else f"SEVENBITS" if bytesize == 7 else f"SIXBITS" if bytesize == 6 else "FIVEBITS"),
                parity=getattr(serial, f"PARITY_{'NONE' if parity == 'N' else 'EVEN' if parity == 'E' else 'ODD' if parity == 'O' else 'MARK' if parity == 'M' else 'SPACE'}"),
                stopbits=getattr(serial, f"STOPBITS_{'ONE' if stopbits == 1 else 'ONE_POINT_FIVE' if stopbits == 1.5 else 'TWO'}"),
                timeout=timeout,
                write_timeout=write_timeout,
                xonxoff=xonxoff,
                rtscts=rtscts,
                dsrdtr=dsrdtr
            )
            
            # 打开串口
            if not self._serial.is_open:
                self._serial.open()
                
            # 清空缓冲区
            self._serial.reset_input_buffer()
            self._serial.reset_output_buffer()
            
            # 更新状态
            self._is_connected = True

            # 启动读取线程
            self._start_read_thread()

            # 发送连接成功信号
            self._signals.connected.emit()
            
            logger.info(f"已连接到串口 {port}，波特率: {baudrate}")
            return True
            
        except serial.SerialException as e:
            error_msg = f"连接串口失败: {str(e)}"
            logger.error(error_msg)
            # 发送错误信号
            self._signals.error_occurred.emit(ConnectionError(error_msg))
            raise ConnectionError(error_msg) from e
    
    def disconnect(self) -> bool:
        """
        断开串口连接
        
        Returns:
            断开连接是否成功
        """
        if not self._is_connected:
            logger.warning("串口未连接")
            return True
            
        try:
            # 停止读取线程
            self._stop_read_thread()
            
            # 关闭串口
            if self._serial and self._serial.is_open:
                self._serial.close()
                
            # 更新状态
            self._is_connected = False
            
            # 发送断开连接信号
            self._signals.disconnected.emit()
            
            logger.info("已断开串口连接")
            return True
            
        except Exception as e:
            logger.error(f"断开串口连接失败: {str(e)}")
            return False

    def _start_read_thread(self) -> None:
        """启动读取线程"""
        if not hasattr(self, '_read_thread') or not self._read_thread or not self._read_thread.is_alive():
            self._is_running = True
            self._read_thread = threading.Thread(target=self._read_loop, daemon=True)
            self._read_thread.start()
            logger.debug("读取线程已启动")

    def _stop_read_thread(self) -> None:
        """停止读取线程"""
        self._is_running = False
        if hasattr(self, '_read_thread') and self._read_thread and self._read_thread.is_alive():
            self._read_thread.join(timeout=1.0)
            logger.debug("读取线程已停止")

    def _read_loop(self) -> None:
        """
        读取循环，在单独线程中运行
        
        持续从串口读取数据，并通过信号发送到主线程
        """
        read_mode = self._config["read_mode"]
        encoding = self._config["encoding"]
        
        # 确保eol是字节类型
        eol = self._config["eol"]
        if isinstance(eol, str):
            eol = eol.encode(encoding)
        elif not isinstance(eol, bytes):
            eol = b"\n"  # 默认使用换行符
        
        logger.debug("串口读取线程已启动")
        
        while self._is_running and self._is_connected and self._serial and self._serial.is_open:
            try:
                if read_mode == "line":
                    # 按行读取
                    line = self._serial.readline()
                    if line:
                        # 解码数据
                        try:
                            # 去除行尾的换行符
                            if isinstance(line, bytes) and isinstance(eol, bytes) and line.endswith(eol):
                                line = line[:-len(eol)]
                                
                            decoded_line = line.decode(encoding).strip()
                            
                            # 尝试将字符串转换为数字
                            processed_data = decoded_line
                            try:
                                # 尝试转换为整数
                                if decoded_line.isdigit() or (decoded_line.startswith('-') and decoded_line[1:].isdigit()):
                                    processed_data = int(decoded_line)
                                # 尝试转换为浮点数
                                elif '.' in decoded_line:
                                    processed_data = float(decoded_line)
                            except ValueError:
                                # 如果转换失败，保持原字符串
                                pass
                            
                            # 发送数据接收信号
                            self._signals.data_received.emit(processed_data)
                            
                            # 检查是否是命令响应
                            self._check_response(processed_data)
                            
                        except UnicodeDecodeError as e:
                            logger.warning(f"解码数据失败: {str(e)}")
                            # 发送原始字节数据
                            self._signals.data_received.emit(line)
                else:
                    # 原始读取模式（增强版）
                    try:
                        # 检查是否有数据可读
                        if self._serial.in_waiting > 0:
                            # 读取所有可用数据
                            data = self._serial.read(self._serial.in_waiting)
                            if data:
                                logger.debug(f"原始模式接收到 {len(data)} 字节数据: {data.hex().upper()}")

                                # 发送数据接收信号
                                self._signals.data_received.emit(data)

                                # 检查是否是命令响应
                                # 优先使用原始字节数据进行响应检查（适合十六进制协议）
                                self._check_response(data)
                        else:
                            # 没有数据时，尝试读取1个字节（阻塞读取）
                            data = self._serial.read(1)
                            if data:
                                logger.debug(f"原始模式接收到 {len(data)} 字节数据: {data.hex().upper()}")

                                # 检查是否还有更多数据
                                if self._serial.in_waiting > 0:
                                    # 读取剩余数据
                                    additional_data = self._serial.read(self._serial.in_waiting)
                                    data += additional_data
                                    logger.debug(f"读取到额外 {len(additional_data)} 字节，总计: {data.hex().upper()}")

                                # 发送数据接收信号
                                self._signals.data_received.emit(data)

                                # 检查是否是命令响应
                                self._check_response(data)

                            # 同时尝试解码为文本进行兼容性处理
                            try:
                                decoded_data = data.decode(encoding).strip()
                                if decoded_data:  # 只有非空字符串才进行额外处理
                                    # 尝试将字符串转换为数字
                                    processed_data = decoded_data
                                    try:
                                        # 尝试转换为整数
                                        if decoded_data.isdigit() or (decoded_data.startswith('-') and decoded_data[1:].isdigit()):
                                            processed_data = int(decoded_data)
                                        # 尝试转换为浮点数
                                        elif '.' in decoded_data:
                                            processed_data = float(decoded_data)
                                    except ValueError:
                                        # 如果转换失败，保持原字符串
                                        pass

                                    # 如果解码后的数据与原始数据不同，也进行响应检查
                                    if processed_data != data:
                                        self._check_response(processed_data)
                            except UnicodeDecodeError:
                                # 无法解码为文本，这对于十六进制数据是正常的
                                logger.debug(f"数据无法解码为文本，使用原始字节数据: {data.hex().upper()}")
                    except serial.SerialTimeoutException:
                        # 读取超时是正常的，继续循环
                        pass
                
                # 短暂休眠，避免CPU占用过高
                time.sleep(0.01)
                
            except serial.SerialException as e:
                logger.error(f"串口读取错误: {str(e)}")
                # 发送错误信号
                self._signals.error_occurred.emit(ConnectionError(f"串口读取错误: {str(e)}"))
                # 断开连接
                self._is_connected = False
                self._signals.disconnected.emit()
                break
                
            except Exception as e:
                logger.error(f"串口读取线程异常: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                time.sleep(0.1)  # 避免异常导致的高CPU占用
        
        logger.debug("串口读取线程已退出")
    
    def _check_response(self, data: Any) -> None:
        """
        检查数据是否是命令响应

        Args:
            data: 接收到的数据，可能是字符串、数字或字节类型
        """
        with self._response_lock:
            # 添加调试日志
            logger.debug(f"收到潜在响应数据: {repr(data)} 类型: {type(data)}")

            # 检查是否有等待响应的命令
            for cmd_id, response_info in list(self._response_dict.items()):
                if isinstance(response_info, dict) and 'event' in response_info:
                    # 新的响应匹配机制
                    event = response_info['event']
                    expected_response = response_info.get('expected_response')

                    if expected_response:
                        # 进行精确匹配验证
                        if self._match_response(data, expected_response):
                            logger.debug(f"找到匹配的命令ID: {cmd_id}，响应数据匹配成功")
                            self._response_dict[cmd_id] = {
                                'event': event,
                                'data': data,
                                'matched': True,
                                'expected_response': expected_response
                            }
                            event.set()
                            break
                        else:
                            logger.debug(f"命令ID: {cmd_id} 响应数据不匹配，期望: {expected_response}")
                    else:
                        # 兼容旧的响应处理方式（没有期望响应的情况）
                        logger.debug(f"找到匹配的命令ID: {cmd_id}，设置响应数据（无期望响应）")
                        self._response_dict[cmd_id] = {
                            'event': event,
                            'data': data,
                            'matched': True,
                            'expected_response': None
                        }
                        event.set()
                        break
                elif isinstance(response_info, threading.Event):
                    # 兼容旧的响应处理方式
                    logger.debug(f"找到匹配的命令ID: {cmd_id}，设置响应数据（旧格式）")
                    self._response_dict[cmd_id] = data
                    response_info.set()
                    break

    def _match_response(self, received_data: Any, expected_response: str) -> bool:
        """
        匹配接收到的数据与期望的响应

        Args:
            received_data: 接收到的数据
            expected_response: 期望的响应（十六进制字符串格式）

        Returns:
            是否匹配成功
        """
        try:
            # 将期望响应转换为字节数据
            expected_hex = expected_response.replace(' ', '').upper()
            expected_bytes = bytes.fromhex(expected_hex)

            # 处理接收到的数据
            if isinstance(received_data, bytes):
                received_bytes = received_data
            elif isinstance(received_data, str):
                # 如果是字符串，尝试解析为十六进制
                try:
                    received_hex = received_data.replace(' ', '').upper()
                    received_bytes = bytes.fromhex(received_hex)
                except ValueError:
                    # 如果不是十六进制字符串，编码为字节
                    received_bytes = received_data.encode('utf-8')
            else:
                # 其他类型，转换为字符串再编码
                received_bytes = str(received_data).encode('utf-8')

            # 进行字节级前缀匹配（支持响应后跟随额外数据的情况）
            if len(received_bytes) >= len(expected_bytes):
                # 检查接收数据是否以期望响应开头
                match_result = received_bytes[:len(expected_bytes)] == expected_bytes
            else:
                # 接收数据长度不足
                match_result = False

            if match_result:
                logger.debug(f"响应匹配成功: 接收={received_bytes.hex().upper()}, 期望={expected_hex}")
                if len(received_bytes) > len(expected_bytes):
                    extra_data = received_bytes[len(expected_bytes):]
                    logger.debug(f"检测到额外数据: {extra_data.hex().upper()}")
            else:
                logger.debug(f"响应匹配失败: 接收={received_bytes.hex().upper()}, 期望={expected_hex}")

            return match_result

        except Exception as e:
            logger.error(f"响应匹配过程中发生错误: {e}")
            return False

    def read_data(self, timeout: float = 1.0) -> Any:
        """
        读取数据（同步）
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            读取到的数据
            
        Raises:
            TimeoutError: 读取超时时抛出
            ConnectionError: 未连接或连接异常时抛出
        """
        if not self._is_connected:
            raise ConnectionError("串口未连接")
            
        if not self._serial or not self._serial.is_open:
            raise ConnectionError("串口连接异常")
            
        try:
            read_mode = self._config["read_mode"]
            eol = self._config["eol"]
            encoding = self._config["encoding"]
            
            # 保存原超时设置
            old_timeout = self._serial.timeout
            self._serial.timeout = timeout
            
            try:
                if read_mode == "line":
                    # 按行读取
                    line = self._serial.readline()
                    if not line:
                        raise TimeoutError("读取数据超时")
                        
                    # 去除行尾的换行符
                    if line.endswith(eol):
                        line = line[:-len(eol)]
                        
                    # 解码数据
                    try:
                        return line.decode(encoding)
                    except UnicodeDecodeError:
                        return line
                else:
                    # 原始读取模式 - 智能数据包读取
                    buffer = b''
                    start_time = time.time()

                    while time.time() - start_time < timeout:
                        if self._serial.in_waiting > 0:
                            chunk = self._serial.read(self._serial.in_waiting)
                            buffer += chunk

                            # 检查是否有完整的IM948数据包
                            if self._has_complete_im948_packet(buffer):
                                packet = self._extract_im948_packet(buffer)
                                if packet:
                                    return packet
                        else:
                            # 短暂等待更多数据
                            time.sleep(0.001)

                    # 如果没有找到完整数据包，返回缓冲区中的所有数据
                    if buffer:
                        return buffer
                    else:
                        raise TimeoutError("读取数据超时")
            finally:
                # 恢复原超时设置
                self._serial.timeout = old_timeout
                
        except serial.SerialException as e:
            logger.error(f"读取数据失败: {str(e)}")
            raise ConnectionError(f"读取数据失败: {str(e)}") from e

    def _has_complete_im948_packet(self, buffer: bytes) -> bool:
        """
        检查缓冲区是否包含完整的IM948数据包

        Args:
            buffer: 数据缓冲区

        Returns:
            是否包含完整数据包
        """
        if len(buffer) < 6:  # 最小包长度
            return False

        # 查找IM948数据包头 (49 00)
        for i in range(len(buffer) - 5):
            if buffer[i] == 0x49 and buffer[i + 1] == 0x00:
                # 检查是否是数据包 (第3个字节应该是长度)
                if i + 2 < len(buffer):
                    packet_length = buffer[i + 2] + 4  # 长度字段 + 头部和尾部
                    if i + packet_length <= len(buffer):
                        # 检查尾部是否是4D
                        if buffer[i + packet_length - 1] == 0x4D:
                            return True
        return False

    def _extract_im948_packet(self, buffer: bytes) -> bytes:
        """
        从缓冲区提取第一个完整的IM948数据包

        Args:
            buffer: 数据缓冲区

        Returns:
            完整的数据包，如果没有找到则返回None
        """
        if len(buffer) < 6:
            return None

        # 查找IM948数据包头 (49 00)
        for i in range(len(buffer) - 5):
            if buffer[i] == 0x49 and buffer[i + 1] == 0x00:
                # 检查是否是数据包
                if i + 2 < len(buffer):
                    packet_length = buffer[i + 2] + 4  # 长度字段 + 头部和尾部
                    if i + packet_length <= len(buffer):
                        # 检查尾部是否是4D
                        if buffer[i + packet_length - 1] == 0x4D:
                            return buffer[i:i + packet_length]
        return None

    def send_command(self, command: Union[str, bytes], wait_response: bool = False,
                    timeout: float = 1.0, expected_response: Optional[str] = None) -> Optional[Any]:
        """
        发送命令

        Args:
            command: 要发送的命令，字符串或字节
            wait_response: 是否等待响应
            timeout: 等待响应的超时时间（秒）
            expected_response: 期望的响应数据（十六进制字符串格式），用于精确匹配验证

        Returns:
            如果wait_response为True，返回响应数据或响应信息字典；否则返回None

        Raises:
            CommandError: 命令发送失败时抛出
            TimeoutError: 等待响应超时时抛出
            ConnectionError: 未连接或连接异常时抛出
        """
        if not self._is_connected:
            raise ConnectionError("串口未连接")
            
        if not self._serial or not self._serial.is_open:
            raise ConnectionError("串口连接异常")
            
        try:
            encoding = self._config["encoding"]
            eol = self._config["eol"]
            
            # 将命令转换为字节
            if isinstance(command, str):
                if not command.endswith("\n") and eol == b"\n":
                    command += "\n"
                command_bytes = command.encode(encoding)
            else:
                command_bytes = command
                
            # 生成命令ID
            cmd_id = id(command_bytes)
            response_event = None
            
            # 如果需要等待响应，创建一个事件
            if wait_response:
                response_event = threading.Event()
                with self._response_lock:
                    if expected_response:
                        # 使用新的响应匹配机制
                        self._response_dict[cmd_id] = {
                            'event': response_event,
                            'expected_response': expected_response,
                            'data': None,
                            'matched': False
                        }
                    else:
                        # 兼容旧的响应处理方式
                        self._response_dict[cmd_id] = response_event
            
            # 发送命令
            bytes_written = self._serial.write(command_bytes)
            
            if bytes_written != len(command_bytes):
                raise CommandError(f"命令发送不完整: 已发送 {bytes_written} 字节，共 {len(command_bytes)} 字节")
                
            # 发送命令已发送事件
            self._handle_event(ProtocolEventType.COMMAND_SENT, command)
            
            # 等待响应
            if wait_response:
                if not response_event.wait(timeout):
                    # 超时，移除响应事件
                    with self._response_lock:
                        self._response_dict.pop(cmd_id, None)
                    raise TimeoutError("等待响应超时")
                    
                # 获取响应数据
                with self._response_lock:
                    response_info = self._response_dict.pop(cmd_id, None)

                # 处理不同格式的响应数据
                if isinstance(response_info, dict):
                    # 新的响应匹配机制
                    if expected_response:
                        # 返回完整的响应信息，包括匹配状态
                        return {
                            'data': response_info.get('data'),
                            'matched': response_info.get('matched', False),
                            'expected_response': response_info.get('expected_response'),
                            'success': response_info.get('matched', False)
                        }
                    else:
                        # 没有期望响应，返回原始数据
                        return response_info.get('data')
                else:
                    # 兼容旧的响应处理方式
                    return response_info
            
            return None
            
        except serial.SerialException as e:
            logger.error(f"发送命令失败: {str(e)}")
            raise CommandError(f"发送命令失败: {str(e)}") from e

    def send_command_with_validation(self, command: Union[str, bytes], expected_response: str,
                                   timeout: float = 1.0) -> bool:
        """
        发送命令并验证响应是否匹配期望值

        Args:
            command: 要发送的命令
            expected_response: 期望的响应数据（十六进制字符串格式）
            timeout: 等待响应的超时时间（秒）

        Returns:
            响应是否匹配期望值

        Raises:
            CommandError: 命令发送失败时抛出
            TimeoutError: 等待响应超时时抛出
            ConnectionError: 未连接或连接异常时抛出
        """
        try:
            response_info = self.send_command(command, wait_response=True,
                                            timeout=timeout, expected_response=expected_response)

            if isinstance(response_info, dict):
                return response_info.get('success', False)
            else:
                # 兼容旧格式，进行手动匹配
                return self._match_response(response_info, expected_response)

        except Exception as e:
            logger.error(f"发送命令并验证响应失败: {e}")
            raise

    def clear_buffer(self) -> None:
        """
        清空串口接收缓冲区
        """
        if self._serial and self._serial.is_open:
            self._serial.reset_input_buffer()  # 清空输入缓冲区
            logger.info("串口接收缓冲区已清空") 