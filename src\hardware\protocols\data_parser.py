#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据解析引擎模块

提供工业自动化协议数据的解析、转换和验证功能。
支持多种数据格式、单位转换、数据验证和统计分析。

Author: Augment Agent
Date: 2025-08-01
"""

import logging
import struct
import math
from typing import Any, Dict, List, Optional, Union, Tuple
from enum import Enum
from datetime import datetime

# 获取logger
logger = logging.getLogger(__name__)


class DataType(Enum):
    """数据类型枚举"""
    TEMPERATURE = "temperature"
    HUMIDITY = "humidity"
    PRESSURE = "pressure"
    VOLTAGE = "voltage"
    CURRENT = "current"
    POWER = "power"
    FLOW = "flow"
    LEVEL = "level"
    PH = "ph"
    CONDUCTIVITY = "conductivity"
    GENERIC = "generic"


class UnitConverter:
    """单位转换器"""
    
    # 单位转换表
    CONVERSION_TABLE = {
        # 温度转换
        'temperature': {
            ('°C', '°F'): lambda x: x * 9/5 + 32,
            ('°F', '°C'): lambda x: (x - 32) * 5/9,
            ('°C', 'K'): lambda x: x + 273.15,
            ('K', '°C'): lambda x: x - 273.15,
        },
        # 压力转换
        'pressure': {
            ('Pa', 'kPa'): lambda x: x / 1000,
            ('kPa', 'Pa'): lambda x: x * 1000,
            ('kPa', 'bar'): lambda x: x / 100,
            ('bar', 'kPa'): lambda x: x * 100,
            ('bar', 'psi'): lambda x: x * 14.5038,
            ('psi', 'bar'): lambda x: x / 14.5038,
        },
        # 电压转换
        'voltage': {
            ('V', 'mV'): lambda x: x * 1000,
            ('mV', 'V'): lambda x: x / 1000,
            ('V', 'kV'): lambda x: x / 1000,
            ('kV', 'V'): lambda x: x * 1000,
        },
        # 电流转换
        'current': {
            ('A', 'mA'): lambda x: x * 1000,
            ('mA', 'A'): lambda x: x / 1000,
            ('A', 'μA'): lambda x: x * 1000000,
            ('μA', 'A'): lambda x: x / 1000000,
        }
    }
    
    @classmethod
    def convert(cls, value: float, from_unit: str, to_unit: str, data_type: str = 'generic') -> float:
        """
        单位转换
        
        Args:
            value: 原始值
            from_unit: 源单位
            to_unit: 目标单位
            data_type: 数据类型
            
        Returns:
            转换后的值
        """
        if from_unit == to_unit:
            return value
        
        conversion_key = (from_unit, to_unit)
        
        if data_type in cls.CONVERSION_TABLE:
            conversions = cls.CONVERSION_TABLE[data_type]
            if conversion_key in conversions:
                return conversions[conversion_key](value)
        
        # 如果没有找到特定转换，尝试通用转换
        for category_conversions in cls.CONVERSION_TABLE.values():
            if conversion_key in category_conversions:
                return category_conversions[conversion_key](value)
        
        logger.warning(f"未找到单位转换: {from_unit} -> {to_unit}")
        return value


class DataValidator:
    """数据验证器"""
    
    # 数据范围定义
    VALID_RANGES = {
        DataType.TEMPERATURE: (-273.15, 1000),  # °C
        DataType.HUMIDITY: (0, 100),            # %RH
        DataType.PRESSURE: (0, 10000),          # kPa
        DataType.VOLTAGE: (-1000, 1000),        # V
        DataType.CURRENT: (-1000, 1000),        # A
        DataType.PH: (0, 14),                   # pH
    }
    
    @classmethod
    def validate_range(cls, value: float, data_type: DataType, custom_range: Optional[Tuple[float, float]] = None) -> bool:
        """
        验证数据范围
        
        Args:
            value: 数据值
            data_type: 数据类型
            custom_range: 自定义范围
            
        Returns:
            验证结果
        """
        if custom_range:
            min_val, max_val = custom_range
        elif data_type in cls.VALID_RANGES:
            min_val, max_val = cls.VALID_RANGES[data_type]
        else:
            return True  # 未知类型，不验证
        
        return min_val <= value <= max_val
    
    @classmethod
    def validate_change_rate(cls, current_value: float, previous_value: float, max_change_rate: float) -> bool:
        """
        验证变化率
        
        Args:
            current_value: 当前值
            previous_value: 前一个值
            max_change_rate: 最大变化率（百分比）
            
        Returns:
            验证结果
        """
        if previous_value == 0:
            return True
        
        change_rate = abs((current_value - previous_value) / previous_value) * 100
        return change_rate <= max_change_rate


class DataParser:
    """
    数据解析引擎
    
    提供工业协议数据的解析、转换、验证和统计功能。
    """
    
    def __init__(self):
        """初始化数据解析引擎"""
        self.unit_converter = UnitConverter()
        self.data_validator = DataValidator()
        self.parsing_history: List[Dict[str, Any]] = []
        
        logger.debug("数据解析引擎已初始化")
    
    def parse_sensor_data(self, raw_data: Dict[str, Any], parsing_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析传感器数据
        
        Args:
            raw_data: 原始数据字典
            parsing_config: 解析配置
            
        Returns:
            解析后的数据字典
        """
        try:
            logger.debug(f"开始解析传感器数据: {raw_data}")
            
            parsed_result = {
                'timestamp': datetime.now().isoformat(),
                'raw_data': raw_data,
                'parsed_data': {},
                'validation_results': {},
                'warnings': []
            }
            
            # 解析每个数据字段
            for field_name, field_data in raw_data.items():
                if field_name in parsing_config:
                    field_config = parsing_config[field_name]
                    parsed_field = self._parse_single_field(field_data, field_config)
                    parsed_result['parsed_data'][field_name] = parsed_field
                    
                    # 数据验证
                    validation_result = self._validate_field_data(parsed_field, field_config)
                    parsed_result['validation_results'][field_name] = validation_result
                    
                    if not validation_result['valid']:
                        parsed_result['warnings'].extend(validation_result['warnings'])
            
            # 记录解析历史
            self.parsing_history.append(parsed_result)
            
            # 保持历史记录在合理范围内
            if len(self.parsing_history) > 1000:
                self.parsing_history = self.parsing_history[-1000:]
            
            logger.debug(f"数据解析完成: {parsed_result['parsed_data']}")
            return parsed_result
            
        except Exception as e:
            logger.error(f"数据解析失败: {e}")
            raise ValueError(f"数据解析失败: {e}") from e
    
    def _parse_single_field(self, field_data: Dict[str, Any], field_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析单个数据字段
        
        Args:
            field_data: 字段数据
            field_config: 字段配置
            
        Returns:
            解析后的字段数据
        """
        result = {
            'raw_value': field_data.get('value'),
            'unit': field_data.get('unit', ''),
            'data_type': field_config.get('data_type', DataType.GENERIC.value)
        }
        
        # 应用数据转换
        processed_value = result['raw_value']

        # 应用缩放因子（scale）
        if 'scale' in field_config:
            processed_value *= field_config['scale']

        # 应用偏移量
        if 'offset' in field_config:
            processed_value += field_config['offset']

        # 应用乘数
        if 'multiplier' in field_config:
            processed_value *= field_config['multiplier']
        
        # 应用自定义函数
        if 'transform_function' in field_config:
            func_name = field_config['transform_function']
            if hasattr(self, f'_transform_{func_name}'):
                transform_func = getattr(self, f'_transform_{func_name}')
                processed_value = transform_func(processed_value)
        
        result['processed_value'] = processed_value
        
        # 单位转换
        target_unit = field_config.get('target_unit')
        if target_unit and target_unit != result['unit']:
            converted_value = self.unit_converter.convert(
                processed_value,
                result['unit'],
                target_unit,
                result['data_type']
            )
            result['converted_value'] = converted_value
            result['converted_unit'] = target_unit
        else:
            result['converted_value'] = processed_value
            result['converted_unit'] = result['unit']
        
        # 精度处理
        precision = field_config.get('precision', 2)
        result['final_value'] = round(result['converted_value'], precision)
        
        return result
    
    def _validate_field_data(self, field_data: Dict[str, Any], field_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证字段数据
        
        Args:
            field_data: 字段数据
            field_config: 字段配置
            
        Returns:
            验证结果
        """
        validation_result = {
            'valid': True,
            'warnings': []
        }
        
        value = field_data['final_value']
        data_type_str = field_data['data_type']
        
        try:
            data_type = DataType(data_type_str)
        except ValueError:
            data_type = DataType.GENERIC
        
        # 范围验证
        custom_range = field_config.get('valid_range')
        if not self.data_validator.validate_range(value, data_type, custom_range):
            validation_result['valid'] = False
            validation_result['warnings'].append(f"数值超出有效范围: {value}")
        
        # 变化率验证
        max_change_rate = field_config.get('max_change_rate')
        if max_change_rate and len(self.parsing_history) > 0:
            last_record = self.parsing_history[-1]
            field_name = None
            for name, data in last_record['parsed_data'].items():
                if data == field_data:
                    field_name = name
                    break
            
            if field_name and field_name in last_record['parsed_data']:
                previous_value = last_record['parsed_data'][field_name]['final_value']
                if not self.data_validator.validate_change_rate(value, previous_value, max_change_rate):
                    validation_result['valid'] = False
                    validation_result['warnings'].append(f"数值变化率过大: {value} (前值: {previous_value})")
        
        return validation_result
    
    def get_statistics(self, field_name: str, time_window: int = 100) -> Dict[str, Any]:
        """
        获取字段统计信息
        
        Args:
            field_name: 字段名称
            time_window: 时间窗口（记录数量）
            
        Returns:
            统计信息字典
        """
        if not self.parsing_history:
            return {}
        
        # 提取指定字段的数据
        values = []
        recent_history = self.parsing_history[-time_window:]
        
        for record in recent_history:
            if field_name in record['parsed_data']:
                values.append(record['parsed_data'][field_name]['final_value'])
        
        if not values:
            return {}
        
        # 计算统计信息
        stats = {
            'count': len(values),
            'min': min(values),
            'max': max(values),
            'mean': sum(values) / len(values),
            'latest': values[-1]
        }
        
        # 计算标准差
        if len(values) > 1:
            variance = sum((x - stats['mean']) ** 2 for x in values) / (len(values) - 1)
            stats['std_dev'] = math.sqrt(variance)
        else:
            stats['std_dev'] = 0
        
        return stats
    
    def clear_history(self) -> None:
        """清空解析历史"""
        self.parsing_history.clear()
        logger.info("解析历史已清空")
    
    # 自定义转换函数示例
    def _transform_linearize(self, value: float) -> float:
        """线性化转换"""
        return value
    
    def _transform_logarithmic(self, value: float) -> float:
        """对数转换"""
        return math.log10(max(value, 0.001))  # 避免log(0)
    
    def _transform_exponential(self, value: float) -> float:
        """指数转换"""
        return math.exp(min(value, 100))  # 避免溢出
