#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
协议配置解析器模块

实现工业自动化协议配置文件的加载、验证和解析功能。
支持JSON格式的配置文件，提供完整的配置验证和错误处理。

Author: Augment Agent
Date: 2025-08-01
"""

import json
import logging
import os
from typing import Any, Dict, List, Optional, Union
from pathlib import Path
try:
    import jsonschema
    from jsonschema import validate, ValidationError
    HAS_JSONSCHEMA = True
except ImportError:
    HAS_JSONSCHEMA = False
    print("警告: jsonschema库未安装，将跳过配置文件验证。建议运行: pip install jsonschema")

# 获取logger
logger = logging.getLogger(__name__)


class ProtocolConfigParser:
    """
    协议配置解析器
    
    提供协议配置文件的加载、验证和解析功能。
    支持JSON格式配置文件，包含完整的配置验证和错误处理。
    """
    
    def __init__(self):
        """初始化配置解析器"""
        self.config_schema = self._get_config_schema()
        self.loaded_configs: Dict[str, Dict[str, Any]] = {}
        
        logger.debug("协议配置解析器已初始化")
    
    def load_config(self, config_path: Union[str, Path]) -> Dict[str, Any]:
        """
        加载协议配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置字典
            
        Raises:
            FileNotFoundError: 配置文件不存在
            ValueError: 配置文件格式错误或验证失败
        """
        try:
            config_path = Path(config_path)
            
            if not config_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {config_path}")
            
            logger.info(f"加载配置文件: {config_path}")
            
            # 读取JSON配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 验证配置格式
            self.validate_config(config_data)
            
            # 缓存配置
            config_key = str(config_path.absolute())
            self.loaded_configs[config_key] = config_data
            
            logger.info(f"配置文件加载成功: {config_data.get('device_name', 'Unknown')}")
            return config_data
            
        except json.JSONDecodeError as e:
            error_msg = f"JSON格式错误: {e}"
            logger.error(error_msg)
            raise ValueError(error_msg) from e
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        验证配置文件格式
        
        Args:
            config: 配置字典
            
        Returns:
            验证结果
            
        Raises:
            ValueError: 配置验证失败
        """
        try:
            logger.debug("开始验证配置格式")

            # 使用JSON Schema验证（如果可用）
            if HAS_JSONSCHEMA:
                validate(instance=config, schema=self.config_schema)
            else:
                logger.warning("跳过JSON Schema验证（jsonschema库未安装）")

            # 额外的业务逻辑验证
            self._validate_business_logic(config)

            logger.debug("配置验证通过")
            return True

        except Exception as e:
            if HAS_JSONSCHEMA and hasattr(e, 'message'):
                error_msg = f"配置格式验证失败: {e.message}"
            else:
                error_msg = f"配置验证失败: {e}"
            logger.error(error_msg)
            raise ValueError(error_msg) from e
        except Exception as e:
            logger.error(f"配置验证异常: {e}")
            raise ValueError(f"配置验证失败: {e}") from e
    
    def get_device_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取设备配置信息
        
        Args:
            config: 完整配置字典
            
        Returns:
            设备配置字典
        """
        return {
            'device_name': config.get('device_name', ''),
            'device_type': config.get('device_type', ''),
            'description': config.get('description', ''),
            'connection': config.get('connection', {}),
            'protocol_type': config.get('protocol', {}).get('type', 'hex_template')
        }
    
    def get_protocol_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取协议配置信息
        
        Args:
            config: 完整配置字典
            
        Returns:
            协议配置字典
        """
        protocol_config = config.get('protocol', {})
        return {
            'type': protocol_config.get('type', 'hex_template'),
            'encoding': protocol_config.get('encoding', 'hex'),
            'frame_format': protocol_config.get('frame_format', {}),
            'commands': protocol_config.get('commands', {})
        }
    
    def get_command_config(self, config: Dict[str, Any], command_name: str) -> Optional[Dict[str, Any]]:
        """
        获取指定命令的配置
        
        Args:
            config: 完整配置字典
            command_name: 命令名称
            
        Returns:
            命令配置字典，如果不存在返回None
        """
        protocol_config = self.get_protocol_config(config)
        commands = protocol_config.get('commands', {})
        return commands.get(command_name)
    
    def list_available_commands(self, config: Dict[str, Any]) -> List[str]:
        """
        列出可用的命令列表
        
        Args:
            config: 完整配置字典
            
        Returns:
            命令名称列表
        """
        protocol_config = self.get_protocol_config(config)
        commands = protocol_config.get('commands', {})
        return list(commands.keys())
    
    def _validate_business_logic(self, config: Dict[str, Any]) -> None:
        """
        验证业务逻辑
        
        Args:
            config: 配置字典
            
        Raises:
            ValueError: 业务逻辑验证失败
        """
        # 验证协议类型
        protocol_config = config.get('protocol', {})
        protocol_type = protocol_config.get('type', '')
        
        if protocol_type not in ['hex_template', 'ascii_template', 'modbus_rtu']:
            raise ValueError(f"不支持的协议类型: {protocol_type}")
        
        # 验证命令配置
        commands = protocol_config.get('commands', {})
        if not commands:
            raise ValueError("至少需要配置一个命令")
        
        for command_name, command_config in commands.items():
            # 获取指令类型
            command_type = command_config.get('type', 'one_time')

            # 验证请求配置（持续指令可以没有请求配置）
            if command_type == 'one_time':
                if 'request' not in command_config:
                    raise ValueError(f"一次性命令 {command_name} 缺少请求配置")

                request_config = command_config['request']
                if 'template' not in request_config:
                    raise ValueError(f"命令 {command_name} 缺少请求模板")

                if 'parameters' not in request_config:
                    raise ValueError(f"命令 {command_name} 缺少请求参数")

            # 验证响应配置
            if 'response' not in command_config:
                raise ValueError(f"命令 {command_name} 缺少响应配置")
        
        # 验证连接配置
        connection_config = config.get('connection', {})
        if 'port' not in connection_config:
            raise ValueError("缺少串口配置")
        
        logger.debug("业务逻辑验证通过")
    
    def _get_config_schema(self) -> Dict[str, Any]:
        """
        获取配置文件的JSON Schema
        
        Returns:
            JSON Schema字典
        """
        return {
            "type": "object",
            "required": ["device_name", "protocol", "connection"],
            "properties": {
                "device_name": {"type": "string"},
                "device_type": {"type": "string"},
                "description": {"type": "string"},
                "connection": {
                    "type": "object",
                    "required": ["port"],
                    "properties": {
                        "port": {"type": "string"},
                        "baudrate": {"type": "integer", "minimum": 1200, "maximum": 115200},
                        "bytesize": {"type": "integer", "enum": [5, 6, 7, 8]},
                        "parity": {"type": "string", "enum": ["N", "E", "O", "M", "S"]},
                        "stopbits": {"type": "number", "enum": [1, 1.5, 2]},
                        "timeout": {"type": "number", "minimum": 0.1, "maximum": 30.0}
                    }
                },
                "protocol": {
                    "type": "object",
                    "required": ["type", "commands"],
                    "properties": {
                        "type": {"type": "string", "enum": ["hex_template", "ascii_template", "modbus_rtu"]},
                        "encoding": {"type": "string"},
                        "frame_format": {"type": "object"},
                        "commands": {
                            "type": "object",
                            "patternProperties": {
                                "^[a-zA-Z_][a-zA-Z0-9_]*$": {
                                    "type": "object",
                                    "properties": {
                                        "type": {
                                            "type": "string",
                                            "enum": ["one_time", "continuous"],
                                            "description": "指令类型：one_time为一次性指令，continuous为持续数据流指令"
                                        },
                                        "description": {"type": "string"},
                                        "timeout": {"type": "number"},
                                        "retry_count": {"type": "integer"},
                                        "request": {
                                            "type": "object",
                                            "required": ["template", "parameters"],
                                            "properties": {
                                                "template": {"type": "string"},
                                                "parameters": {"type": "object"}
                                            }
                                        },
                                        "response": {
                                            "type": "object",
                                            "properties": {
                                                "template": {"type": "string"},
                                                "expected_response": {
                                                    "type": "string",
                                                    "pattern": "^[0-9A-Fa-f\\s]+$",
                                                    "description": "期望的应答数据（十六进制格式，可包含空格）"
                                                },
                                                "fields": {"type": "object"},
                                                "data_parsing": {
                                                    "type": "object",
                                                    "properties": {
                                                        "success_condition": {
                                                            "type": "string",
                                                            "enum": ["exact_match", "status == 0"],
                                                            "description": "成功条件：exact_match为精确匹配，status == 0为状态字段匹配"
                                                        }
                                                    }
                                                }
                                            }
                                        },
                                        "subscription_config": {
                                            "type": "object",
                                            "description": "持续指令的订阅配置",
                                            "properties": {
                                                "max_data_count": {"type": "integer", "minimum": 1},
                                                "data_interval": {"type": "number", "minimum": 0.001},
                                                "auto_start": {"type": "boolean"}
                                            }
                                        }
                                    },
                                    "allOf": [
                                        {
                                            "if": {"properties": {"type": {"const": "one_time"}}},
                                            "then": {"required": ["request", "response"]}
                                        },
                                        {
                                            "if": {"properties": {"type": {"const": "continuous"}}},
                                            "then": {"required": ["response"]}
                                        }
                                    ]
                                }
                            }
                        }
                    }
                }
            }
        }
    
    def create_sample_config(self, output_path: Union[str, Path]) -> None:
        """
        创建示例配置文件
        
        Args:
            output_path: 输出文件路径
        """
        sample_config = {
            "device_name": "温度传感器示例",
            "device_type": "temperature_sensor",
            "description": "十六进制协议温度传感器示例配置",
            "connection": {
                "port": "COM2",
                "baudrate": 9600,
                "bytesize": 8,
                "parity": "N",
                "stopbits": 1,
                "timeout": 2.0
            },
            "protocol": {
                "type": "hex_template",
                "encoding": "hex",
                "frame_format": {
                    "header": "AA BB",
                    "footer": "CC",
                    "checksum": "sum8"
                },
                "commands": {
                    "read_temperature": {
                        "description": "读取温度值",
                        "timeout": 3.0,
                        "retry_count": 3,
                        "request": {
                            "template": "#{header} #{device_id} #{function_code} #{data_length} #{register_addr} #{checksum} #{footer}",
                            "parameters": {
                                "header": "AA BB",
                                "device_id": "01",
                                "function_code": "03",
                                "data_length": "02",
                                "register_addr": "00 01",
                                "checksum": "auto_sum8",
                                "footer": "CC"
                            }
                        },
                        "response": {
                            "fields": {
                                "header": {"type": "fixed", "value": "AA BB", "position": 0},
                                "device_id": {"type": "match", "position": 2},
                                "function_code": {"type": "match", "position": 3},
                                "data_length": {"type": "length", "position": 4},
                                "data": {"type": "data", "position": 5, "length_field": "data_length"},
                                "checksum": {"type": "checksum", "method": "sum8", "position": -2},
                                "footer": {"type": "fixed", "value": "CC", "position": -1}
                            },
                            "data_parsing": {
                                "temperature": {
                                    "offset": 0,
                                    "length": 2,
                                    "format": "uint16_be",
                                    "scale": 0.1,
                                    "unit": "°C"
                                }
                            }
                        }
                    }
                }
            }
        }
        
        output_path = Path(output_path)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(sample_config, f, ensure_ascii=False, indent=2)
        
        logger.info(f"示例配置文件已创建: {output_path}")

    # ==================== 持续指令支持方法 ====================

    def get_command_type(self, config: Dict[str, Any], command_name: str) -> Optional[str]:
        """
        获取指定命令的类型

        Args:
            config: 完整配置字典
            command_name: 命令名称

        Returns:
            命令类型：'one_time' 或 'continuous'，如果不存在返回None
        """
        command_config = self.get_command_config(config, command_name)
        if command_config:
            return command_config.get('type', 'one_time')  # 默认为一次性指令
        return None

    def is_continuous_command(self, config: Dict[str, Any], command_name: str) -> bool:
        """
        判断指定命令是否为持续指令

        Args:
            config: 完整配置字典
            command_name: 命令名称

        Returns:
            是否为持续指令
        """
        command_type = self.get_command_type(config, command_name)
        return command_type == 'continuous'

    def get_continuous_commands(self, config: Dict[str, Any]) -> List[str]:
        """
        获取所有持续指令的名称列表

        Args:
            config: 完整配置字典

        Returns:
            持续指令名称列表
        """
        continuous_commands = []
        protocol_config = self.get_protocol_config(config)
        commands = protocol_config.get('commands', {})

        for command_name, command_config in commands.items():
            if command_config.get('type') == 'continuous':
                continuous_commands.append(command_name)

        return continuous_commands

    def get_one_time_commands(self, config: Dict[str, Any]) -> List[str]:
        """
        获取所有一次性指令的名称列表

        Args:
            config: 完整配置字典

        Returns:
            一次性指令名称列表
        """
        one_time_commands = []
        protocol_config = self.get_protocol_config(config)
        commands = protocol_config.get('commands', {})

        for command_name, command_config in commands.items():
            command_type = command_config.get('type', 'one_time')
            if command_type == 'one_time':
                one_time_commands.append(command_name)

        return one_time_commands

    def get_subscription_config(self, config: Dict[str, Any], command_name: str) -> Optional[Dict[str, Any]]:
        """
        获取指定持续指令的订阅配置

        Args:
            config: 完整配置字典
            command_name: 命令名称

        Returns:
            订阅配置字典，如果不是持续指令或不存在返回None
        """
        command_config = self.get_command_config(config, command_name)
        if command_config and command_config.get('type') == 'continuous':
            # 获取订阅配置，提供默认值
            subscription_config = command_config.get('subscription_config', {})

            # 设置默认值
            default_config = {
                'max_data_count': 1000,
                'data_interval': 0.1,
                'auto_start': False
            }

            # 合并配置
            result_config = default_config.copy()
            result_config.update(subscription_config)

            return result_config

        return None

    def validate_command_for_subscription(self, config: Dict[str, Any], command_name: str) -> bool:
        """
        验证指定命令是否可用于订阅

        Args:
            config: 完整配置字典
            command_name: 命令名称

        Returns:
            是否可用于订阅

        Raises:
            ValueError: 验证失败时抛出异常
        """
        command_config = self.get_command_config(config, command_name)

        if not command_config:
            raise ValueError(f"命令 {command_name} 不存在")

        command_type = command_config.get('type', 'one_time')
        if command_type != 'continuous':
            raise ValueError(f"命令 {command_name} 不是持续类型，无法用于订阅")

        # 验证必要的响应配置
        if 'response' not in command_config:
            raise ValueError(f"持续命令 {command_name} 缺少响应配置")

        logger.debug(f"命令 {command_name} 订阅验证通过")
        return True

    # ==================== 应答匹配支持方法 ====================

    def get_expected_response(self, config: Dict[str, Any], command_name: str) -> Optional[str]:
        """
        获取指定命令的期望应答数据

        Args:
            config: 完整配置字典
            command_name: 命令名称

        Returns:
            期望应答的十六进制字符串，如果不存在返回None
        """
        command_config = self.get_command_config(config, command_name)
        if command_config:
            response_config = command_config.get('response', {})
            return response_config.get('expected_response')
        return None

    def has_expected_response(self, config: Dict[str, Any], command_name: str) -> bool:
        """
        判断指定命令是否配置了期望应答

        Args:
            config: 完整配置字典
            command_name: 命令名称

        Returns:
            是否配置了期望应答
        """
        expected_response = self.get_expected_response(config, command_name)
        return expected_response is not None and expected_response.strip() != ""

    def get_response_success_condition(self, config: Dict[str, Any], command_name: str) -> Optional[str]:
        """
        获取指定命令的响应成功条件

        Args:
            config: 完整配置字典
            command_name: 命令名称

        Returns:
            成功条件字符串，如果不存在返回None
        """
        command_config = self.get_command_config(config, command_name)
        if command_config:
            response_config = command_config.get('response', {})
            data_parsing = response_config.get('data_parsing', {})
            return data_parsing.get('success_condition')
        return None

    def validate_response_config(self, config: Dict[str, Any], command_name: str) -> bool:
        """
        验证指定命令的响应配置

        Args:
            config: 完整配置字典
            command_name: 命令名称

        Returns:
            验证是否通过

        Raises:
            ValueError: 验证失败时抛出异常
        """
        command_config = self.get_command_config(config, command_name)

        if not command_config:
            raise ValueError(f"命令 {command_name} 不存在")

        response_config = command_config.get('response', {})
        if not response_config:
            logger.warning(f"命令 {command_name} 没有响应配置")
            return True  # 允许没有响应配置的命令

        # 检查期望应答配置
        expected_response = response_config.get('expected_response')
        if expected_response:
            # 验证期望应答格式（应该是十六进制字符串）
            try:
                # 移除空格并验证是否为有效的十六进制
                hex_str = expected_response.replace(' ', '')
                if len(hex_str) % 2 != 0:
                    raise ValueError(f"命令 {command_name} 的期望应答长度不是偶数")
                bytes.fromhex(hex_str)
            except ValueError as e:
                raise ValueError(f"命令 {command_name} 的期望应答格式无效: {e}")

        # 检查成功条件
        success_condition = self.get_response_success_condition(config, command_name)
        if success_condition and success_condition not in ['exact_match', 'status == 0']:
            logger.warning(f"命令 {command_name} 使用了未知的成功条件: {success_condition}")

        logger.debug(f"命令 {command_name} 响应配置验证通过")
        return True
