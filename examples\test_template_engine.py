#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模板引擎测试脚本

测试十六进制模板引擎的渲染行为

Author: Augment Agent
Date: 2025-08-02
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.hardware.protocols.hex_template_engine import HexTemplateEngine
from src.hardware.protocols.protocol_config_parser import ProtocolConfigParser

def test_template_engine():
    """测试模板引擎"""
    engine = HexTemplateEngine()

    # 测试固定模板（不应该被修改）
    template = "49000B1205FF00041E010305C0000C4D"
    parameters = {}

    print(f"原始模板: {template}")
    print(f"参数: {parameters}")

    try:
        result = engine.render_template(template, parameters)
        print(f"渲染结果: {result.hex().upper()}")

        if result.hex().upper() == template:
            print("✅ 模板渲染正确")
        else:
            print("❌ 模板渲染错误")
            print(f"期望: {template}")
            print(f"实际: {result.hex().upper()}")

    except Exception as e:
        print(f"❌ 模板渲染异常: {e}")

def test_config_loading():
    """测试配置文件加载"""
    parser = ProtocolConfigParser()
    config_path = project_root / "examples" / "im948_config.json"

    print(f"\n=== 测试配置文件加载 ===")
    print(f"配置文件路径: {config_path}")

    try:
        config = parser.load_config(config_path)

        # 获取set_subscription命令配置
        cmd_config = parser.get_command_config(config, 'set_subscription')
        if cmd_config:
            template = cmd_config['request']['template']
            parameters = cmd_config['request']['parameters']

            print(f"命令模板: {template}")
            print(f"命令参数: {parameters}")

            # 测试模板渲染
            engine = HexTemplateEngine()
            result = engine.render_template(template, parameters)
            print(f"渲染结果: {result.hex().upper()}")

            expected = "49000B1205FF00041E010305C0000C4D"
            if result.hex().upper() == expected:
                print("✅ 配置文件模板渲染正确")
            else:
                print("❌ 配置文件模板渲染错误")
                print(f"期望: {expected}")
                print(f"实际: {result.hex().upper()}")
        else:
            print("❌ 未找到set_subscription命令配置")

    except Exception as e:
        print(f"❌ 配置文件测试异常: {e}")

if __name__ == "__main__":
    test_template_engine()
    test_config_loading()
