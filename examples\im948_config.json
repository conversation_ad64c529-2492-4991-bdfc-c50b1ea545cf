{"device_name": "IM948惯性测量单元", "device_type": "IMU", "description": "IM948九轴惯性测量单元，支持欧拉角和三维位置数据订阅", "connection": {"port": "COM6", "baudrate": 115200, "bytesize": 8, "parity": "N", "stopbits": 1, "timeout": 1.0}, "protocol": {"type": "hex_template", "encoding": "utf-8", "frame_format": {"header": "49 00", "length_field": true, "footer": "4D"}, "commands": {"set_subscription": {"type": "one_time", "description": "设置功能订阅，启用欧拉角(bit6)和三维位置(bit7)数据输出", "timeout": 2.0, "retry_count": 3, "request": {"template": "49000B1205FF00041E010305C0000C4D", "parameters": {}}, "response": {"expected_response": "49000112134D", "template": "49000112134D", "fields": {}, "validation": {"success_condition": "exact_match"}}}, "start_subscription": {"type": "one_time", "description": "启动数据订阅，开始自动上报数据", "timeout": 2.0, "retry_count": 3, "request": {"template": "490001191A4D", "parameters": {}}, "response": {"expected_response": "490001191A4D", "template": "490001191A4D", "fields": {}, "validation": {"success_condition": "exact_match"}}}, "get_euler_angles": {"type": "continuous", "description": "获取欧拉角数据流 (Roll, Pitch, Yaw)", "subscription_config": {"max_data_count": 1000, "data_interval": 0.01, "auto_start": false}, "response": {"template": "49 00 13 11 {subscription_mask:04X} {timestamp:08X} {roll:04X} {pitch:04X} {yaw:04X} {x:04X} {y:04X} {z:04X} {checksum:02X} 4D", "fields": {"subscription_mask": {"type": "uint16_le", "description": "订阅掩码"}, "timestamp": {"type": "uint32_le", "unit": "ms", "description": "时间戳"}, "roll": {"type": "int16_le", "scale": 0.0054931640625, "unit": "度", "description": "横滚角"}, "pitch": {"type": "int16_le", "scale": 0.0054931640625, "unit": "度", "description": "俯仰角"}, "yaw": {"type": "int16_le", "scale": 0.0054931640625, "unit": "度", "description": "偏航角"}, "x": {"type": "int16_le", "scale": 0.001, "unit": "m", "description": "X轴位置"}, "y": {"type": "int16_le", "scale": 0.001, "unit": "m", "description": "Y轴位置"}, "z": {"type": "int16_le", "scale": 0.001, "unit": "m", "description": "Z轴位置"}, "data": {"type": "data", "position": 6, "length": 17}}, "data_parsing": {"roll": {"offset": 4, "length": 2, "format": "int16_le", "scale": 0.0054931640625, "unit": "°"}, "pitch": {"offset": 6, "length": 2, "format": "int16_le", "scale": 0.0054931640625, "unit": "°"}, "yaw": {"offset": 8, "length": 2, "format": "int16_le", "scale": 0.0054931640625, "unit": "°"}, "x": {"offset": 10, "length": 2, "format": "int16_le", "scale": 0.001, "unit": "m"}, "y": {"offset": 12, "length": 2, "format": "int16_le", "scale": 0.001, "unit": "m"}, "z": {"offset": 14, "length": 2, "format": "int16_le", "scale": 0.001, "unit": "m"}}, "validation": {"roll": {"min": -180.0, "max": 180.0}, "pitch": {"min": -90.0, "max": 90.0}, "yaw": {"min": -180.0, "max": 180.0}, "x": {"min": -1000.0, "max": 1000.0}, "y": {"min": -1000.0, "max": 1000.0}, "z": {"min": -1000.0, "max": 1000.0}}}}, "stop_subscription": {"type": "one_time", "description": "停止所有数据订阅", "timeout": 2.0, "retry_count": 3, "request": {"template": "49000B1205FF00041E010305000000154D", "parameters": {}}, "response": {"template": "49 00 02 92 {status:02X} {checksum:02X} 4D", "fields": {"status": {"type": "uint8", "description": "停止状态，0x00表示成功"}}, "validation": {"success_condition": "status == 0"}}}, "get_device_info": {"type": "one_time", "description": "获取设备信息", "timeout": 2.0, "retry_count": 3, "request": {"template": "49000101014D", "parameters": {}}, "response": {"template": "49000C81", "fields": {"device_id": {"type": "uint16_be", "description": "设备ID"}, "firmware_version": {"type": "uint16_be", "description": "固件版本"}, "hardware_version": {"type": "uint16_be", "description": "硬件版本"}, "serial_number": {"type": "uint16_be", "description": "序列号"}}}}}}}