#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
十六进制串口通信快速测试脚本
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.hardware.protocols.serial_protocol import SerialProtocol
from src.hardware.protocol_base import ProtocolEventType

def hex_string_to_bytes(hex_str: str) -> bytes:
    """将十六进制字符串转换为字节数据"""
    hex_str = hex_str.replace(' ', '').replace('-', '').replace(':', '').replace(',', '')
    if len(hex_str) % 2 != 0:
        hex_str = '0' + hex_str
    return bytes.fromhex(hex_str)

def on_data_received(data):
    """数据接收回调"""
    if isinstance(data, bytes):
        hex_data = ' '.join([f'{b:02X}' for b in data])
        ascii_data = ''.join([chr(b) if 32 <= b <= 126 else '.' for b in data])
        print(f"[接收] 字节数据 (长度: {len(data)})")
        print(f"       HEX: {hex_data}")
        print(f"       ASCII: {ascii_data}")
    else:
        print(f"[接收] 数据: {data}")

def test_com2_hex():
    """测试COM2十六进制通信"""
    print("=== COM2 十六进制通信测试 ===")

    # 创建串口实例
    serial_protocol = SerialProtocol("COM2")

    # 配置为原始模式
    serial_protocol.set_config({
        "baudrate": 9600,
        "bytesize": 8,
        "parity": "N",
        "stopbits": 1,
        "timeout": 0.1,  # 减少超时时间以提高响应性
        "read_mode": "raw"
    })

    # 注册所有事件回调
    serial_protocol.register_event_callback(ProtocolEventType.DATA_RECEIVED, on_data_received)
    serial_protocol.register_event_callback(ProtocolEventType.CONNECTED, lambda: print("🔗 串口已连接"))
    serial_protocol.register_event_callback(ProtocolEventType.DISCONNECTED, lambda: print("🔌 串口已断开"))
    serial_protocol.register_event_callback(ProtocolEventType.ERROR, lambda e: print(f"❌ 串口错误: {e}"))

    try:
        # 连接串口
        if serial_protocol.connect():
            print("✅ 成功连接到COM2")

            # 启动读取线程
            start_result = serial_protocol.start_reading()
            print(f"✅ 数据读取线程启动结果: {start_result}")

            # 检查线程状态
            if hasattr(serial_protocol, '_read_thread') and serial_protocol._read_thread:
                print(f"📊 读取线程状态: alive={serial_protocol._read_thread.is_alive()}")

            print(f"📊 连接状态: {serial_protocol._is_connected}")
            print(f"📊 运行状态: {serial_protocol._is_running}")

            print("\n现在请从COM3发送数据 (如: 53 53 53)...")
            print("程序将显示接收到的内容...")

            # 分段等待，每5秒显示一次状态
            for segment in range(6):  # 总共30秒，分6段
                print(f"\n--- 等待数据 第{segment+1}/6段 (5秒) ---")
                for i in range(5):
                    print(f"等待中... {5-i}秒", end='\r')
                    time.sleep(1)

                # 显示线程状态
                if hasattr(serial_protocol, '_read_thread') and serial_protocol._read_thread:
                    print(f"\n📊 读取线程仍在运行: {serial_protocol._read_thread.is_alive()}")

            print("\n\n=== 测试发送十六进制数据 ===")

            # 发送测试数据
            test_data = hex_string_to_bytes("48 65 6C 6C 6F")  # "Hello"
            print(f"📤 发送数据: {test_data.hex().upper()} (ASCII: Hello)")
            serial_protocol.send_command(test_data, wait_response=False)

            # 再等待5秒看是否有响应
            print("等待5秒查看响应...")
            for i in range(5):
                print(f"等待响应... {5-i}秒", end='\r')
                time.sleep(1)

            print("\n\n=== 清理资源 ===")
            # 断开连接
            serial_protocol.stop_reading()
            serial_protocol.disconnect()
            print("✅ 已断开连接")

        else:
            print("❌ 连接COM2失败")

    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_com2_hex()
