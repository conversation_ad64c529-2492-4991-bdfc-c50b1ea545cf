#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
双向串口通信测试脚本
同时打开COM2和COM3进行双向通信测试
"""

import sys
import os
import time
import threading

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.hardware.protocols.serial_protocol import SerialProtocol
from src.hardware.protocol_base import ProtocolEventType

def on_data_received_com2(data):
    """COM2数据接收回调"""
    if isinstance(data, bytes):
        hex_data = ' '.join([f'{b:02X}' for b in data])
        ascii_data = ''.join([chr(b) if 32 <= b <= 126 else '.' for b in data])
        print(f"[COM2接收] HEX: {hex_data} | ASCII: {ascii_data}")
    else:
        print(f"[COM2接收] 数据: {data}")

def on_data_received_com3(data):
    """COM3数据接收回调"""
    if isinstance(data, bytes):
        hex_data = ' '.join([f'{b:02X}' for b in data])
        ascii_data = ''.join([chr(b) if 32 <= b <= 126 else '.' for b in data])
        print(f"[COM3接收] HEX: {hex_data} | ASCII: {ascii_data}")
    else:
        print(f"[COM3接收] 数据: {data}")

def test_dual_communication():
    """测试双向串口通信"""
    print("=== 双向串口通信测试 ===")
    
    # 创建两个串口实例
    com2 = SerialProtocol("COM2")
    com3 = SerialProtocol("COM3")
    
    # 配置串口参数
    config = {
        "baudrate": 9600,
        "bytesize": 8,
        "parity": "N",
        "stopbits": 1,
        "timeout": 0.1,
        "read_mode": "raw"
    }
    
    com2.set_config(config)
    com3.set_config(config)
    
    # 注册回调
    com2.register_event_callback(ProtocolEventType.DATA_RECEIVED, on_data_received_com2)
    com3.register_event_callback(ProtocolEventType.DATA_RECEIVED, on_data_received_com3)
    
    try:
        # 连接两个串口
        print("连接COM2...")
        if not com2.connect():
            print("❌ COM2连接失败")
            return
        print("✅ COM2连接成功")
        
        print("连接COM3...")
        if not com3.connect():
            print("❌ COM3连接失败")
            com2.disconnect()
            return
        print("✅ COM3连接成功")
        
        # 启动读取线程
        com2.start_reading()
        com3.start_reading()
        print("✅ 读取线程已启动")
        
        # 测试1: COM2发送到COM3
        print("\n=== 测试1: COM2 -> COM3 ===")
        test_data = b"\x53\x53\x53"  # 十六进制 53 53 53
        print(f"COM2发送: {test_data.hex().upper()}")
        com2.send_command(test_data, wait_response=False)
        time.sleep(2)
        
        # 测试2: COM3发送到COM2
        print("\n=== 测试2: COM3 -> COM2 ===")
        test_data = b"Hello"
        print(f"COM3发送: {test_data.hex().upper()} (ASCII: {test_data.decode()})")
        com3.send_command(test_data, wait_response=False)
        time.sleep(2)
        
        # 测试3: 连续发送
        print("\n=== 测试3: 连续发送 ===")
        for i in range(3):
            data = bytes([0x41 + i])  # A, B, C
            print(f"COM2发送: {data.hex().upper()} (ASCII: {data.decode()})")
            com2.send_command(data, wait_response=False)
            time.sleep(1)
        
        print("\n等待5秒接收剩余数据...")
        time.sleep(5)
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        print("\n=== 清理资源 ===")
        try:
            com2.stop_reading()
            com3.stop_reading()
            com2.disconnect()
            com3.disconnect()
            print("✅ 资源清理完成")
        except Exception as e:
            print(f"⚠️ 清理资源时出现异常: {e}")

if __name__ == "__main__":
    test_dual_communication()
