#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
十六进制模板引擎模块

实现工业自动化十六进制协议的模板渲染、数据解析和校验计算功能。
支持灵活的占位符替换和多种校验算法。

Author: Augment Agent
Date: 2025-08-01
"""

import logging
import re
import struct
from typing import Any, Dict, List, Optional, Union, Callable
from enum import Enum

# 获取logger
logger = logging.getLogger(__name__)


class ChecksumType(Enum):
    """校验和类型枚举"""
    SUM8 = "sum8"           # 8位累加和
    XOR = "xor"             # 异或校验
    CRC16 = "crc16"         # CRC16校验
    CS = "cs"               # CS校验和（256-sum）
    MODBUS_CRC = "modbus_crc"  # Modbus CRC16


class DataFormat(Enum):
    """数据格式类型枚举"""
    UINT8 = "uint8"         # 无符号8位整数
    INT8 = "int8"           # 有符号8位整数
    UINT16_BE = "uint16_be" # 无符号16位整数（大端）
    UINT16_LE = "uint16_le" # 无符号16位整数（小端）
    INT16_BE = "int16_be"   # 有符号16位整数（大端）
    INT16_LE = "int16_le"   # 有符号16位整数（小端）
    UINT32_BE = "uint32_be" # 无符号32位整数（大端）
    UINT32_LE = "uint32_le" # 无符号32位整数（小端）
    INT32_BE = "int32_be"   # 有符号32位整数（大端）
    INT32_LE = "int32_le"   # 有符号32位整数（小端）
    FLOAT32_BE = "float32_be" # 32位浮点数（大端）
    FLOAT32_LE = "float32_le" # 32位浮点数（小端）


class HexTemplateEngine:
    """
    十六进制模板引擎
    
    提供十六进制协议的模板渲染、数据解析和校验计算功能。
    支持占位符替换、自动校验和计算、多种数据格式解析。
    """
    
    def __init__(self):
        """初始化模板引擎"""
        self.checksum_methods: Dict[str, Callable[[bytes], int]] = {
            ChecksumType.SUM8.value: self._calc_sum8,
            ChecksumType.XOR.value: self._calc_xor,
            ChecksumType.CRC16.value: self._calc_crc16,
            ChecksumType.CS.value: self._calc_cs,
            ChecksumType.MODBUS_CRC.value: self._calc_modbus_crc
        }
        
        # 占位符正则表达式 - 支持两种格式：#{key} 和 {key:format}
        self.placeholder_pattern = re.compile(r'#\{([^}]+)\}')
        self.python_format_pattern = re.compile(r'\{([^}:]+)(?::[^}]*)?\}')
        
        logger.debug("十六进制模板引擎已初始化")
    
    def render_template(self, template: str, parameters: Dict[str, Any]) -> bytes:
        """
        渲染十六进制模板

        Args:
            template: 模板字符串，支持两种格式：
                     - #{key} 格式：#{header} #{device_id} #{command}
                     - {key:format} 格式：{subscription_mask:02X} {checksum:02X}
            parameters: 参数字典

        Returns:
            渲染后的字节数组

        Raises:
            ValueError: 模板格式错误或参数缺失
        """
        try:
            logger.debug(f"开始渲染模板: {template}")
            logger.debug(f"参数: {parameters}")

            # 检查是否为固定的十六进制字符串（无占位符）
            if not self.python_format_pattern.search(template) and not self.placeholder_pattern.search(template):
                # 固定模板，直接转换为字节数组
                logger.debug("检测到固定模板，跳过参数替换和校验和计算")
                result_bytes = self._hex_string_to_bytes(template)
                logger.debug(f"固定模板渲染完成: {result_bytes.hex().upper()}")
                return result_bytes

            # 处理自动校验和（仅当有占位符时）
            processed_template, processed_params = self._process_auto_checksum(template, parameters)

            # 检测模板格式并处理
            if self.python_format_pattern.search(processed_template):
                # Python字符串格式化语法 {key:format}
                result_string = self._render_python_format(processed_template, processed_params)
            else:
                # 传统格式 #{key}
                result_string = self._render_traditional_format(processed_template, processed_params)

            # 转换为字节数组
            result_bytes = self._hex_string_to_bytes(result_string)
            logger.debug(f"模板渲染完成: {result_bytes.hex().upper()}")

            return result_bytes

        except Exception as e:
            logger.error(f"模板渲染失败: {e}")
            raise ValueError(f"模板渲染失败: {e}") from e

    def _render_python_format(self, template: str, parameters: Dict[str, Any]) -> str:
        """
        使用Python字符串格式化语法渲染模板

        Args:
            template: 包含{key:format}格式占位符的模板
            parameters: 参数字典

        Returns:
            渲染后的字符串
        """
        # 转换参数值为合适的格式
        formatted_params = {}
        for key, value in parameters.items():
            if isinstance(value, str) and value.startswith('0x'):
                # 十六进制字符串转换为整数
                formatted_params[key] = int(value, 16)
            else:
                formatted_params[key] = value

        # 使用Python字符串格式化
        result = template.format(**formatted_params)
        logger.debug(f"Python格式化完成: {result}")
        return result

    def _render_traditional_format(self, template: str, parameters: Dict[str, Any]) -> str:
        """
        使用传统#{key}格式渲染模板

        Args:
            template: 包含#{key}格式占位符的模板
            parameters: 参数字典

        Returns:
            渲染后的字符串
        """
        result_string = template
        for key, value in parameters.items():
            placeholder = f"#{{{key}}}"
            if placeholder in result_string:
                result_string = result_string.replace(placeholder, str(value))
                logger.debug(f"替换占位符 {placeholder} -> {value}")

        # 检查是否还有未替换的占位符
        remaining_placeholders = self.placeholder_pattern.findall(result_string)
        if remaining_placeholders:
            raise ValueError(f"模板中存在未替换的占位符: {remaining_placeholders}")

        return result_string

    def parse_response(self, response: bytes, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        简化的响应解析，基于固定偏移量

        Args:
            response: 响应字节数组
            config: 解析配置

        Returns:
            解析后的数据字典

        Raises:
            ValueError: 数据解析失败
        """
        try:
            logger.debug(f"开始解析响应: {response.hex().upper()}")
            logger.debug(f"解析配置: {config}")

            result = {}

            # 直接基于data_parsing进行解析（验证已在adapter中完成）
            if 'data_parsing' not in config:
                logger.warning("配置中缺少data_parsing，返回空结果")
                return {}

            # 直接使用原始响应数据进行解析
            for field_name, parse_config in config['data_parsing'].items():
                try:
                    value = self._parse_data_field(response, parse_config)
                    result[field_name] = {
                        'value': value,
                        'unit': parse_config.get('unit', ''),
                        'raw_data': response[parse_config['offset']:parse_config['offset'] + parse_config['length']].hex().upper()
                    }
                    logger.debug(f"解析字段 {field_name}: {value} {parse_config.get('unit', '')}")
                except Exception as e:
                    logger.error(f"解析字段{field_name}失败: {e}")
                    raise ValueError(f"数据解析失败: {e}")

            logger.debug(f"响应解析完成: {result}")
            return result

        except Exception as e:
            logger.error(f"响应解析失败: {e}")
            raise ValueError(f"响应解析失败: {e}") from e

    def _process_auto_checksum(self, template: str, parameters: Dict[str, Any]) -> tuple[str, Dict[str, Any]]:
        """
        处理自动校验和计算

        Args:
            template: 原始模板
            parameters: 原始参数

        Returns:
            处理后的模板和参数
        """
        processed_params = parameters.copy()

        # 检查是否有checksum占位符需要自动计算
        if 'checksum' in template and 'checksum' not in parameters:
            # 自动计算校验和
            checksum_type = 'sum8'  # 默认使用sum8

            # 检测模板格式
            if self.python_format_pattern.search(template):
                # Python格式：移除checksum占位符
                temp_template = re.sub(r'\{checksum[^}]*\}', '', template).strip()

                # 替换其他占位符
                temp_params = {}
                for temp_key, temp_value in processed_params.items():
                    if isinstance(temp_value, str) and temp_value.startswith('0x'):
                        temp_params[temp_key] = int(temp_value, 16)
                    else:
                        temp_params[temp_key] = temp_value

                try:
                    temp_string = temp_template.format(**temp_params)
                except KeyError:
                    # 如果有缺失的参数，跳过校验和计算
                    return template, processed_params

            else:
                # 传统格式：移除checksum占位符
                temp_template = template.replace(f"#{{{key}}}", '')

                # 替换其他占位符
                temp_string = temp_template
                for temp_key, temp_value in processed_params.items():
                    if not str(temp_value).startswith('auto_'):
                        temp_string = temp_string.replace(f"#{{{temp_key}}}", str(temp_value))

            # 计算校验和
            try:
                temp_bytes = self._hex_string_to_bytes(temp_string)
                checksum = self.checksum_methods[checksum_type](temp_bytes)
                processed_params['checksum'] = checksum
                logger.debug(f"自动计算校验和: {checksum_type} = {checksum:02X}")
            except Exception as e:
                logger.warning(f"校验和计算失败: {e}")

        # 处理显式的auto_校验和参数
        for key, value in parameters.items():
            if isinstance(value, str) and value.startswith('auto_'):
                checksum_type = value.replace('auto_', '')
                if checksum_type in self.checksum_methods:
                    # 这里可以添加更复杂的校验和计算逻辑
                    pass

        return template, processed_params

    def _hex_string_to_bytes(self, hex_string: str) -> bytes:
        """
        十六进制字符串转字节数组

        Args:
            hex_string: 十六进制字符串

        Returns:
            字节数组
        """
        # 移除空格和常见分隔符
        hex_clean = hex_string.replace(' ', '').replace('-', '').replace(':', '').replace(',', '')

        # 确保长度为偶数
        if len(hex_clean) % 2 != 0:
            hex_clean = '0' + hex_clean

        try:
            return bytes.fromhex(hex_clean)
        except ValueError as e:
            raise ValueError(f"无效的十六进制字符串: {hex_string}") from e

    def _validate_frame(self, response: bytes, config: Dict[str, Any]) -> bool:
        """
        验证响应帧格式

        Args:
            response: 响应字节数组
            config: 验证配置

        Returns:
            验证结果
        """
        try:
            if 'fields' not in config:
                return True

            fields = config['fields']

            # 验证固定字段
            for field_name, field_config in fields.items():
                if field_config.get('type') == 'fixed':
                    expected_value = field_config['value']
                    position = field_config.get('position', 0)
                    expected_bytes = self._hex_string_to_bytes(expected_value)

                    # 处理负索引
                    if position < 0:
                        if abs(position) > len(response):
                            logger.warning(f"响应长度不足，无法验证字段 {field_name}")
                            return False
                        actual_bytes = response[position:position + len(expected_bytes)]
                    else:
                        if position + len(expected_bytes) > len(response):
                            logger.warning(f"响应长度不足，无法验证字段 {field_name}")
                            return False
                        actual_bytes = response[position:position + len(expected_bytes)]

                    if actual_bytes != expected_bytes:
                        logger.warning(f"字段 {field_name} 验证失败: 期望 {expected_bytes.hex().upper()}, 实际 {actual_bytes.hex().upper()}")
                        return False

            # 验证校验和
            if 'checksum' in fields:
                checksum_config = fields['checksum']
                if checksum_config.get('type') == 'checksum':
                    method = checksum_config.get('method', 'sum8')
                    position = checksum_config.get('position', -1)

                    # 提取校验和字段
                    if position == -1:
                        checksum_bytes = response[-1:]
                    else:
                        checksum_bytes = response[position:position + 1]

                    # 计算期望校验和 - 排除校验和和footer
                    if position == -1:
                        # 校验和在最后一个字节，排除最后两个字节（校验和+footer）
                        data_for_checksum = response[:-2]
                    else:
                        # 校验和在指定位置，排除校验和字节和footer
                        data_for_checksum = response[:position] + response[position + 1:-1]
                    expected_checksum = self.checksum_methods[method](data_for_checksum)
                    actual_checksum = checksum_bytes[0]

                    if actual_checksum != expected_checksum:
                        logger.warning(f"校验和验证失败: 期望 {expected_checksum:02X}, 实际 {actual_checksum:02X}")
                        return False

            return True

        except Exception as e:
            logger.error(f"帧格式验证异常: {e}")
            return False

    def _extract_data_section(self, response: bytes, config: Dict[str, Any]) -> bytes:
        """
        提取数据段

        Args:
            response: 响应字节数组
            config: 配置信息

        Returns:
            数据段字节数组
        """
        if 'fields' not in config:
            return response

        fields = config['fields']

        # 查找数据字段配置
        for field_name, field_config in fields.items():
            if field_config.get('type') == 'data':
                position = field_config.get('position', 0)

                # 获取数据长度
                if 'length_field' in field_config:
                    length_field_name = field_config['length_field']
                    length_field_config = fields[length_field_name]
                    length_position = length_field_config.get('position', 0)
                    data_length = response[length_position]
                elif 'length' in field_config:
                    data_length = field_config['length']
                else:
                    # 默认到响应结束（排除校验和）
                    data_length = len(response) - position - 1

                return response[position:position + data_length]

        # 如果没有找到数据字段配置，返回整个响应
        return response

    def _parse_data_field(self, data: bytes, config: Dict[str, Any]) -> Union[int, float]:
        """
        解析单个数据字段

        Args:
            data: 数据字节数组
            config: 字段配置

        Returns:
            解析后的数值
        """
        offset = config.get('offset', 0)
        length = config.get('length', 1)
        format_type = config.get('format', 'uint8')
        scale = config.get('scale', 1.0)

        # 提取字段数据
        if offset + length > len(data):
            raise ValueError(f"数据长度不足: 需要 {offset + length} 字节，实际 {len(data)} 字节")

        field_data = data[offset:offset + length]

        # 根据格式解析数据
        if format_type == DataFormat.UINT8.value:
            value = field_data[0]
        elif format_type == DataFormat.INT8.value:
            value = struct.unpack('b', field_data)[0]
        elif format_type == DataFormat.UINT16_BE.value:
            value = struct.unpack('>H', field_data)[0]
        elif format_type == DataFormat.UINT16_LE.value:
            value = struct.unpack('<H', field_data)[0]
        elif format_type == DataFormat.INT16_BE.value:
            value = struct.unpack('>h', field_data)[0]
        elif format_type == DataFormat.INT16_LE.value:
            value = struct.unpack('<h', field_data)[0]
        elif format_type == DataFormat.UINT32_BE.value:
            value = struct.unpack('>I', field_data)[0]
        elif format_type == DataFormat.UINT32_LE.value:
            value = struct.unpack('<I', field_data)[0]
        elif format_type == DataFormat.INT32_BE.value:
            value = struct.unpack('>i', field_data)[0]
        elif format_type == DataFormat.INT32_LE.value:
            value = struct.unpack('<i', field_data)[0]
        elif format_type == DataFormat.FLOAT32_BE.value:
            value = struct.unpack('>f', field_data)[0]
        elif format_type == DataFormat.FLOAT32_LE.value:
            value = struct.unpack('<f', field_data)[0]
        else:
            raise ValueError(f"不支持的数据格式: {format_type}")

        # 应用缩放因子
        return value * scale

    # 校验算法实现
    def _calc_sum8(self, data: bytes) -> int:
        """计算8位累加和校验"""
        return sum(data) & 0xFF

    def _calc_xor(self, data: bytes) -> int:
        """计算异或校验"""
        result = 0
        for byte in data:
            result ^= byte
        return result

    def _calc_cs(self, data: bytes) -> int:
        """计算CS校验和（256-sum）"""
        return (256 - (sum(data) & 0xFF)) & 0xFF

    def _calc_crc16(self, data: bytes) -> int:
        """计算CRC16校验"""
        crc = 0xFFFF
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x0001:
                    crc = (crc >> 1) ^ 0xA001
                else:
                    crc >>= 1
        return crc

    def _calc_modbus_crc(self, data: bytes) -> int:
        """计算Modbus CRC16校验"""
        crc = 0xFFFF
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x0001:
                    crc = (crc >> 1) ^ 0xA001
                else:
                    crc >>= 1
        # Modbus CRC返回低字节在前，高字节在后
        return ((crc & 0xFF) << 8) | ((crc >> 8) & 0xFF)

    def validate_template(self, template: str, parameters: Dict[str, Any]) -> bool:
        """
        验证模板和参数的有效性

        Args:
            template: 模板字符串
            parameters: 参数字典

        Returns:
            验证结果
        """
        try:
            # 查找模板中的占位符
            placeholders = self.placeholder_pattern.findall(template)

            # 检查参数是否完整
            for placeholder in placeholders:
                if placeholder not in parameters:
                    logger.error(f"缺少参数: {placeholder}")
                    return False

            # 检查自动校验和类型是否支持
            for key, value in parameters.items():
                if isinstance(value, str) and value.startswith('auto_'):
                    checksum_type = value.replace('auto_', '')
                    if checksum_type not in self.checksum_methods:
                        logger.error(f"不支持的校验和类型: {checksum_type}")
                        return False

            return True

        except Exception as e:
            logger.error(f"模板验证异常: {e}")
            return False
